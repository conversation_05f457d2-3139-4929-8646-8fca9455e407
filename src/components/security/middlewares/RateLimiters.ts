import { rateLimit } from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import { Request, Response } from 'express';
import crypto from 'crypto';

import { redisClient, logger } from '../../../global/services';
import { GenerateCsrfToken } from './CsrfUtils';

// SECURITY HARDENING: Enhanced API rate limiting with lenient thresholds for better UX
export const ApiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: (req: Request) => {
    // HARDENING: Define rate limit rules in a structured, maintainable way.
    // Rules are ordered from most specific to least specific.
    const rateLimitRules = [
      { path: '/password-reset', limit: 8 },
      { path: '/email-verification', limit: 8 },
      { path: '/auth/', limit: 15 },
      { path: '/responses/export', limit: 15 },
      { path: '/responses/', limit: 60 },
      { path: '/surveys/', limit: 60 },
    ];

    const defaultLimit = 80;

    // Find the first matching rule and return its limit.
    for (const rule of rateLimitRules) {
      if (req.path.includes(rule.path)) {
        return rule.limit;
      }
    }

    return defaultLimit;
  },
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  message: {
    success: false,
    status: 'error',
    message: 'Too many requests from this IP, please try again in a minute',
  },
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:api:',
  }),
  keyGenerator: (req: Request) => {
    let key = req.ip || 'unknown';

    // Include user agent hash for additional fingerprinting
    if (req.headers['user-agent']) {
      const uaHash = crypto.createHash('md5').update(req.headers['user-agent']).digest('hex').substring(0, 8);
      key = `${key}:${uaHash}`;
    }

    // Include email for authenticated requests
    if (req.body && req.body.email) {
      key = `${key}:${req.body.email.toLowerCase()}`;
    }

    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('API rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent'],
      sessionId: req.session?.id,
      timestamp: new Date().toISOString(),
    });

    // If a session exists, generate a new CSRF token and send it with the error.
    // This allows the frontend to re-synchronize its state without logging the user out.
    if (req.session) {
      const csrfToken = GenerateCsrfToken(req, res);
      res.status(429).json({
        success: false,
        status: 'error',
        message: 'Too many requests. Please try again in a minute.',
        csrfToken, // Provide a new token to re-sync the frontend
      });
    } else {
      // No session, just send the standard error
      res.status(429).json({
        success: false,
        status: 'error',
        message: 'Too many requests from this IP, please try again in a minute',
      });
    }
  },
  skip: (req: Request) => req.path === '/health',
});

export const AuthLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 15,
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  message: {
    success: false,
    status: 'error',
    message: 'Too many authentication attempts, please try again in a minute',
  },
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:auth:',
  }),
  keyGenerator: (req: Request) => {
    let key = req.ip || 'unknown';
    if (req.body && req.body.email) {
      key = `${key}:${req.body.email.toLowerCase()}`;
    }
    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
    });
    res.status(429).json({
      success: false,
      status: 'error',
      message: 'Too many authentication attempts, please try again in a minute',
    });
  },
  skip: (req: Request) => req.path === '/health',
});

export const PasswordResetLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 8,
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  message: {
    success: false,
    status: 'error',
    message: 'Too many password reset attempts, please try again in a minute',
  },
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:password-reset:',
  }),
  keyGenerator: (req: Request) => {
    let key = req.ip || 'unknown';
    if (req.body && req.body.email) {
      key = `${key}:${req.body.email.toLowerCase()}`;
    }
    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
    });
    res.status(429).json({
      success: false,
      status: 'error',
      message: 'Too many password reset attempts, please try again in a minute',
    });
  },
  skip: (req: Request) => req.path === '/health',
});

export const EmailVerificationLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 8,
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  message: {
    success: false,
    status: 'error',
    message: 'Too many email verification attempts, please try again in a minute',
  },
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:email-verification:',
  }),
  keyGenerator: (req: Request) => {
    let key = req.ip || 'unknown';
    if (req.body && req.body.email) {
      key = `${key}:${req.body.email.toLowerCase()}`;
    }
    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
    });
    res.status(429).json({
      success: false,
      status: 'error',
      message: 'Too many email verification attempts, please try again in a minute',
    });
  },
  skip: (req: Request) => req.path === '/health',
});

export const AccountCreationLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 5,
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  message: {
    success: false,
    status: 'error',
    message: 'Maximum account creation limit reached, please try again in a minute',
  },
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:account-creation:',
  }),
  keyGenerator: (req: Request) => {
    let key = req.ip || 'unknown';
    if (req.body && req.body.email) {
      key = `${key}:${req.body.email.toLowerCase()}`;
    }
    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
    });
    res.status(429).json({
      success: false,
      status: 'error',
      message: 'Maximum account creation limit reached, please try again in a minute',
    });
  },
  skip: (req: Request) => req.path === '/health',
});

// SECURITY FIX: Enhanced progressive delay with IP blocking
export const ProgressiveDelayForFailedLogin = async (req: Request, res: Response, next: Function) => {
  const key = `login-attempts:${req.ip}:${req.body.email?.toLowerCase() || 'unknown'}`;
  const ipBlockKey = `blocked-ip:${req.ip}`;

  try {
    // Check if IP is temporarily blocked
    const isBlocked = await redisClient.get(ipBlockKey);
    if (isBlocked) {
      logger.warn('Blocked IP attempted login', {
        ip: req.ip,
        email: req.body.email?.toLowerCase(),
        userAgent: req.headers['user-agent'],
      });

      return res.status(429).json({
        success: false,
        message: 'IP temporarily blocked due to suspicious activity. Please try again later.',
      });
    }

    const attemptsStr = await redisClient.get(key);
    const attempts = attemptsStr ? parseInt(attemptsStr, 10) : 0;

    // Block IP after 25 failed attempts for 1 hour
    if (attempts >= 15) {
      await redisClient.setex(ipBlockKey, 3600, 'blocked'); // 1 hour block
      logger.warn('IP blocked due to excessive failed login attempts', {
        ip: req.ip,
        attempts,
        email: req.body.email?.toLowerCase(),
      });

      return res.status(429).json({
        success: false,
        message: 'IP temporarily blocked due to excessive failed login attempts.',
      });
    }

    if (attempts > 0) {
      // Exponential backoff with jitter
      const baseDelay = Math.min(Math.pow(2, attempts) * 100, 30000);
      const jitter = Math.random() * 1000; // Add up to 1 second of jitter
      const delay = baseDelay + jitter;

      logger.info(`Progressive delay for login: ${Math.round(delay)}ms`, {
        ip: req.ip,
        attempts,
        email: req.body.email?.toLowerCase(),
      });

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    res.locals.loginAttemptKey = key;
    res.locals.loginAttempts = attempts;

    next();
  } catch (error) {
    logger.error('Error in progressive delay middleware', error);
    next();
  }
};

/**
 * SECURITY FIX: Advanced rate limiting for sensitive operations
 */
export const SensitiveOperationLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 15, // Lenient limit for better UX
  standardHeaders: 'draft-6', // Send RateLimit-* headers that frontend expects
  legacyHeaders: true, // Also send X-RateLimit-* headers for compatibility
  store: new RedisStore({
    sendCommand: (...args: any[]) => (redisClient as any).call(...args),
    prefix: 'rate-limit:sensitive:',
  }),
  keyGenerator: (req: Request, res: Response) => {
    // Combine IP, user agent hash, and account ID for comprehensive tracking
    let key = req.ip || 'unknown';

    if (req.headers['user-agent']) {
      const uaHash = crypto.createHash('md5').update(req.headers['user-agent']).digest('hex').substring(0, 8);
      key = `${key}:${uaHash}`;
    }

    if (res.locals?.accountId) {
      key = `${key}:${res.locals.accountId}`;
    }

    return key;
  },
  handler: (req: Request, res: Response) => {
    logger.warn('Sensitive operation rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      accountId: res.locals?.accountId,
      userAgent: req.headers['user-agent'],
      sessionId: req.session?.id,
    });

    // If a session exists, generate a new CSRF token and send it with the error.
    if (req.session) {
      const csrfToken = GenerateCsrfToken(req, res);
      res.status(429).json({
        success: false,
        message: 'Too many sensitive operations. Please wait before trying again.',
        csrfToken, // Provide a new token to re-sync the frontend
      });
    } else {
      res.status(429).json({
        success: false,
        message: 'Too many sensitive operations. Please wait before trying again.',
      });
    }
  },
});
