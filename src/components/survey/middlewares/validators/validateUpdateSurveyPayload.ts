import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { Var } from '../../../../global/var';

const updateSurveyPayloadSchema = Joi.object({
  surveyId: Joi.string().uuid().required(),
  attribute: Joi.string().valid('title', 'distribution', 'embedUrl', 'tags', 'config', 'respondentDetails').required(),
  value: Joi.when('attribute', {
    switch: [
      { is: 'title', then: Joi.string().required() },
      { is: 'distribution', then: Joi.string().required() },
      {
        is: 'embedUrl',
        then: Joi.string()
          .uri({ scheme: ['https', 'http'] })
          .regex(/^(https?:\/\/)?(localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$|^https:\/\/.*$/)
          .allow(''),
      },
      { is: 'tags', then: Joi.array().items(Joi.string()).min(0).max(1024).required() },
      { is: 'config', then: Joi.object().required() },
      {
        is: 'respondentDetails',
        then: Joi.array()
          .items(
            Joi.object({
              label: Joi.string().required(),
              value: Joi.string().required(),
              inputType: Joi.string().valid('text', 'email', 'select', 'radio', 'checkbox', 'number').required(),
              required: Joi.boolean().optional(),
              placeholder: Joi.string().optional().allow(''),
              options: Joi.when('inputType', {
                is: Joi.string().valid('select', 'radio', 'checkbox'),
                then: Joi.array()
                  .items(
                    Joi.object({
                      value: Joi.string().required(),
                      label: Joi.string().required(),
                    }),
                  )
                  .optional(),
                otherwise: Joi.optional(),
              }),
              defaultValue: Joi.any().optional(),
            }),
          )
          .min(0)
          .max(1024)
          .required(),
      },
    ],
  }),
});

export const validateUpdateSurveyPayload = (req: Request, res: Response, next: NextFunction) => {
  // Check if someone is trying to update the survey type
  if (req.body.attribute === 'type') {
    console.log(`${Var.app.emoji.failure} Survey type cannot be updated`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} Survey type cannot be updated. Please delete the survey and create a new one.`,
    });
  }

  let { error } = updateSurveyPayloadSchema.validate({
    surveyId: req.params.surveyId,
    attribute: req.body.attribute,
    value: req.body.value,
  });

  if (error) {
    console.log(`${Var.app.emoji.failure} Update survey payload not valid`);
    return res.status(400).json({
      success: false,
      message: `${Var.app.emoji.failure} ${error.details[0].message}`,
    });
  }

  console.log(`${Var.app.emoji.success} Update survey payload valid`);
  next();
};
