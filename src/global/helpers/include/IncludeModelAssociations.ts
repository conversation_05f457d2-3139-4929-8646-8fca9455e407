import { accountModel } from '../../../components/account/models';
import { surveyModel } from '../../../components/survey/models';
import { responseModel } from '../../../components/response/models';

export const IncludeModelAssociations = () => {
  // AccountModel & SurveyModel Associations
  accountModel.hasMany(surveyModel, {
    foreignKey: 'account_id',
  });
  surveyModel.belongsTo(accountModel, {
    foreignKey: 'account_id',
  });

  // SurveyModel & ResponseModel Associations
  surveyModel.hasMany(responseModel, {
    foreignKey: 'survey_id',
  });
  responseModel.belongsTo(surveyModel, {
    foreignKey: 'survey_id',
  });

  // AccountModel & ResponseModel Associations
  accountModel.hasMany(responseModel, {
    foreignKey: 'account_id',
  });
  responseModel.belongsTo(accountModel, {
    foreignKey: 'account_id',
  });
};
