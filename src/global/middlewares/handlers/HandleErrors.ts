import { Request, Response, NextFunction, ErrorRequestHandler } from 'express';
import { Var } from '../../var';
import { logger } from '../../services/logger';

export class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const HandleErrors: ErrorRequestHandler = (err: any, req: Request, res: Response, _next: NextFunction) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // SECURITY FIX: Enhanced error logging with security context
  logSecureError(err, req);

  // DEBUG: Console output in development only
  if (Var.node.env === 'dev') {
    console.log(`[DEV] ❌ Error caught by error handler: ${err.name} - ${err.message}`);
    console.log(`[DEV] Error details:`, {
      path: req.path,
      method: req.method,
      statusCode: err.statusCode,
      stack: err.stack?.split('\n').slice(0, 3).join('\n'), // First 3 lines of stack
    });
    sendDevError(err, req, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    // Handle specific error types with debug logging
    if (err.name === 'SequelizeValidationError') {
      logger.debug('Handling Sequelize validation error', {
        originalError: err.message,
        validationErrors: err.errors?.map((e: any) => e.message),
      });
      error = handleSequelizeValidationError(err);
    }

    if (err.name === 'SequelizeUniqueConstraintError') {
      logger.debug('Handling Sequelize unique constraint error', {
        originalError: err.message,
        constraintErrors: err.errors?.map((e: any) => e.message),
      });
      error = handleSequelizeUniqueConstraintError(err);
    }

    if (err.name === 'JsonWebTokenError') {
      logger.debug('Handling JWT error', { originalError: err.message });
      error = handleJWTError(err);
    }

    if (err.name === 'TokenExpiredError') {
      logger.debug('Handling token expired error', { originalError: err.message });
      error = handleTokenExpiredError(err);
    }

    if (err.name === 'ValidationError') {
      logger.debug('Handling validation error', { originalError: err.message });
      error = handleValidationError(err);
    }

    sendProdError(error, req, res);
  }
};

// SECURITY HARDENING: Sanitized development error responses
const sendDevError = (err: any, _req: Request, res: Response) => {
  // Even in development, sanitize sensitive information
  const sanitizedError = {
    ...err,
    message: err.message,
    // Remove potentially sensitive stack trace details
    stack: err.stack ? err.stack.split('\n').slice(0, 10).join('\n') : undefined,
  };

  return res.status(err.statusCode).json({
    success: false,
    status: err.status,
    message: err.message,
    stack: sanitizedError.stack,
    error: sanitizedError,
  });
};

const sendProdError = (err: any, _req: Request, res: Response) => {
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      success: false,
      status: err.status,
      message: getClientSafeMessage(err),
    });
  }

  logger.error('Unhandled error in production', { error: err });
  return res.status(500).json({
    success: false,
    status: 'error',
    message: 'An unexpected error occurred',
  });
};

/**
 * SECURITY FIX: Generate client-safe error messages
 * Prevents information disclosure through error messages
 */
const getClientSafeMessage = (error: any): string => {
  if (Var.node.env === 'dev') {
    return error.message; // Full details in development
  }

  // Production: Generic messages only
  switch (error.statusCode) {
    case 400:
      return 'Invalid request data';
    case 401:
      return 'Authentication required';
    case 403:
      return 'Access denied';
    case 404:
      return 'Resource not found';
    case 409:
      return 'Resource conflict';
    case 422:
      return 'Invalid data provided';
    case 429:
      return 'Too many requests';
    case 500:
      return 'Internal server error';
    default:
      return 'An error occurred while processing your request';
  }
};

const handleSequelizeValidationError = (err: any) => {
  const message = `Invalid input data. ${err.errors.map((el: any) => el.message).join('. ')}`;
  return new AppError(message, 400);
};

const handleSequelizeUniqueConstraintError = (err: any) => {
  const message = `Duplicate field value: ${err.errors.map((el: any) => el.message).join('. ')}. Please use another value!`;
  return new AppError(message, 400);
};

/**
 * SECURITY FIX: Additional error handlers for common security-related errors
 */
const handleJWTError = (_err: any) => {
  const message = 'Invalid authentication token';
  return new AppError(message, 401);
};

const handleTokenExpiredError = (_err: any) => {
  const message = 'Authentication token expired';
  return new AppError(message, 401);
};

const handleValidationError = (_err: any) => {
  const message = 'Validation failed';
  return new AppError(message, 400);
};

/**
 * SECURITY FIX: Enhanced error logging with security context
 * Logs security-relevant information while protecting sensitive data
 */
const logSecureError = (err: any, req: Request) => {
  const securityContext: any = {
    name: err.name,
    message: err.message,
    statusCode: err.statusCode,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    timestamp: new Date().toISOString(),
    // Only log account ID if available, and mask it
    accountId: req.session?.accountId ? req.session.accountId.substring(0, 8) + '...' : 'anonymous',
    // Log if this might be a security-related error
    isSecurityError: isSecurityRelatedError(err),
  };

  // Include stack trace only in development
  if (Var.node.env === 'dev') {
    securityContext.stack = err.stack;
  }

  logger.error('Application error with security context', securityContext);
};

/**
 * Determine if an error is security-related for enhanced logging
 */
const isSecurityRelatedError = (err: any): boolean => {
  const securityErrorNames = ['JsonWebTokenError', 'TokenExpiredError', 'UnauthorizedError', 'ForbiddenError', 'ValidationError'];

  const securityStatusCodes = [401, 403, 429];

  return securityErrorNames.includes(err.name) || securityStatusCodes.includes(err.statusCode) || (err.message && err.message.toLowerCase().includes('csrf'));
};
