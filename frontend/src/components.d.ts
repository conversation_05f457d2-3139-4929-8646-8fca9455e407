/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { TabItem } from "./components/patterns/p-tabnav/types";
export { TabItem } from "./components/patterns/p-tabnav/types";
export namespace Components {
    interface AppRoot {
    }
    interface BasicSettingsSection {
        "survey": any;
        "surveyId": string;
    }
    interface CBanner {
        "position": string;
        "theme": string;
    }
    interface CCard {
        "clickable": boolean;
    }
    interface CMain {
        "variant": string;
    }
    interface EButton {
        "action": string;
        "active": boolean;
        "disabled": boolean;
        "size": string;
        "theme": string;
        "value": any;
        "variant": string;
    }
    interface EButtonOauth {
        "variant": string;
    }
    interface EImage {
        "src": string;
        "variant": string;
        "width": string;
    }
    interface EInput {
        "checked": boolean;
        "label": string;
        "name": string;
        "placeholder": string;
        "type": string;
        "value": string;
    }
    interface ELink {
        "active": boolean;
        "theme": string;
        "url": string;
        "variant": string;
    }
    interface EList {
    }
    interface EListItem {
    }
    interface EPill {
        "color": string;
    }
    interface ESelect {
        "name": string;
        "options": any;
        "resetTrigger": number;
    }
    interface ESpinner {
        "theme": string;
    }
    interface EText {
        "theme": string;
        "variant": string;
        "weight": string;
    }
    interface ETextEditable {
        "active": boolean;
        "attribute": string;
        "bypass": boolean;
        "entity": string;
        "label": string;
        "type": string;
        "value": string;
    }
    interface LRow {
        "align": string;
        "direction": string;
        "justifyContent": string;
        "variant": string;
    }
    interface LSeparator {
        "variant": string;
    }
    interface LSpacer {
        "value": number;
        "variant": string;
    }
    /**
     * Component for creating SenseQuery categories
     */
    interface PCategoryForm {
    }
    interface PDotgrid {
        "height": string;
        "width": string;
    }
    interface PListWithDelete {
        "emptyMessage": string;
        "items": string;
        "name": string;
    }
    /**
     * Global modal component that can be used from any part of the webapp
     */
    interface PModal {
        "closeOnBackdropClick": boolean;
        "isOpen": boolean;
        "modalTitle": string;
    }
    interface PNotification {
        "duration": number;
        "message": string;
        "theme": 'success' | 'danger' | 'info';
    }
    interface PPriorityItem {
        "index": number;
        "item": string;
    }
    /**
     * Component for creating and editing priority items
     */
    interface PPriorityItemForm {
        "editingItem": string;
        "isEditMode": boolean;
    }
    /**
     * Component for creating and editing custom respondent details
     */
    interface PRespondentDetailForm {
        "editingDetail": string;
        "isEditMode": boolean;
    }
    /**
     * Component for displaying a respondent detail item with edit and delete options
     */
    interface PRespondentDetailItem {
        "detail": string;
        "index": number;
    }
    interface PTabnav {
        "activeTab": string;
        "tabs": TabItem[];
    }
    interface PTopbar {
    }
    interface PUserControl {
    }
    interface RespondentSettingsSection {
        "survey": any;
        "surveyId": string;
    }
    interface SensePollSettings {
        "survey": any;
        "surveyId": string;
    }
    interface SensePrioritySettings {
        "survey": any;
        "surveyId": string;
    }
    interface SenseQuerySettings {
        "survey": any;
        "surveyId": string;
    }
    interface SurveyTypeSettingsSection {
        "survey": any;
        "surveyId": string;
    }
    interface VAccount {
    }
    interface VBilling {
    }
    interface VCatchAll {
    }
    interface VCheckout {
        "orderId": string;
    }
    interface VCreateSurvey {
    }
    interface VDeleteAccount {
    }
    interface VDeleteSurvey {
        "surveyId": string;
    }
    interface VEditSurvey {
        "surveyId": string;
    }
    interface VEmbedSurvey {
        "surveyId": string;
    }
    interface VHome {
    }
    /**
     * Login View Component
     * This component provides the user interface for user authentication.
     * It handles user input validation, API communication, and success events.
     * Features:
     * - Email and password input validation
     * - OAuth authentication option
     * - Loading state management
     * - Error handling with user feedback
     * - Responsive design with shadow DOM encapsulation
     * Security Features:
     * - Client-side input validation before API calls
     * - CSRF token handling through ApiWrapper
     * - Secure password input field
     * - Rate limiting protection (handled by backend)
     * User Experience:
     * - Real-time input handling
     * - Loading indicators during authentication
     * - Clear error messages
     * - Links to related pages (signup, password reset)
     * @example <v-login></v-login>
     */
    interface VLogin {
    }
    interface VPasswordReset {
    }
    interface VPaymentFailed {
        "sessionId": string;
    }
    interface VPaymentSuccess {
        "sessionId": string;
    }
    interface VPostOauth {
        "provider": string;
    }
    interface VShareSurvey {
        "surveyId": string;
    }
    interface VSharedSurvey {
        "shareKey": string;
    }
    interface VSignup {
    }
    interface VSupport {
    }
    interface VSurveyResults {
        "surveyId": string;
    }
}
export interface EButtonCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLEButtonElement;
}
export interface EButtonOauthCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLEButtonOauthElement;
}
export interface EInputCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLEInputElement;
}
export interface ESelectCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLESelectElement;
}
export interface ETextEditableCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLETextEditableElement;
}
export interface PCategoryFormCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPCategoryFormElement;
}
export interface PListWithDeleteCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPListWithDeleteElement;
}
export interface PModalCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPModalElement;
}
export interface PPriorityItemCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPPriorityItemElement;
}
export interface PPriorityItemFormCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPPriorityItemFormElement;
}
export interface PRespondentDetailFormCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPRespondentDetailFormElement;
}
export interface PRespondentDetailItemCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPRespondentDetailItemElement;
}
export interface PTabnavCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLPTabnavElement;
}
export interface RespondentSettingsSectionCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLRespondentSettingsSectionElement;
}
export interface VCheckoutCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVCheckoutElement;
}
export interface VDeleteAccountCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVDeleteAccountElement;
}
export interface VHomeCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVHomeElement;
}
export interface VLoginCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVLoginElement;
}
export interface VPostOauthCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVPostOauthElement;
}
export interface VSignupCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLVSignupElement;
}
declare global {
    interface HTMLAppRootElement extends Components.AppRoot, HTMLStencilElement {
    }
    var HTMLAppRootElement: {
        prototype: HTMLAppRootElement;
        new (): HTMLAppRootElement;
    };
    interface HTMLBasicSettingsSectionElement extends Components.BasicSettingsSection, HTMLStencilElement {
    }
    var HTMLBasicSettingsSectionElement: {
        prototype: HTMLBasicSettingsSectionElement;
        new (): HTMLBasicSettingsSectionElement;
    };
    interface HTMLCBannerElement extends Components.CBanner, HTMLStencilElement {
    }
    var HTMLCBannerElement: {
        prototype: HTMLCBannerElement;
        new (): HTMLCBannerElement;
    };
    interface HTMLCCardElement extends Components.CCard, HTMLStencilElement {
    }
    var HTMLCCardElement: {
        prototype: HTMLCCardElement;
        new (): HTMLCCardElement;
    };
    interface HTMLCMainElement extends Components.CMain, HTMLStencilElement {
    }
    var HTMLCMainElement: {
        prototype: HTMLCMainElement;
        new (): HTMLCMainElement;
    };
    interface HTMLEButtonElementEventMap {
        "buttonClickEvent": any;
    }
    interface HTMLEButtonElement extends Components.EButton, HTMLStencilElement {
        addEventListener<K extends keyof HTMLEButtonElementEventMap>(type: K, listener: (this: HTMLEButtonElement, ev: EButtonCustomEvent<HTMLEButtonElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLEButtonElementEventMap>(type: K, listener: (this: HTMLEButtonElement, ev: EButtonCustomEvent<HTMLEButtonElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLEButtonElement: {
        prototype: HTMLEButtonElement;
        new (): HTMLEButtonElement;
    };
    interface HTMLEButtonOauthElementEventMap {
        "routeToEvent": any;
    }
    interface HTMLEButtonOauthElement extends Components.EButtonOauth, HTMLStencilElement {
        addEventListener<K extends keyof HTMLEButtonOauthElementEventMap>(type: K, listener: (this: HTMLEButtonOauthElement, ev: EButtonOauthCustomEvent<HTMLEButtonOauthElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLEButtonOauthElementEventMap>(type: K, listener: (this: HTMLEButtonOauthElement, ev: EButtonOauthCustomEvent<HTMLEButtonOauthElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLEButtonOauthElement: {
        prototype: HTMLEButtonOauthElement;
        new (): HTMLEButtonOauthElement;
    };
    interface HTMLEImageElement extends Components.EImage, HTMLStencilElement {
    }
    var HTMLEImageElement: {
        prototype: HTMLEImageElement;
        new (): HTMLEImageElement;
    };
    interface HTMLEInputElementEventMap {
        "inputEvent": any;
    }
    interface HTMLEInputElement extends Components.EInput, HTMLStencilElement {
        addEventListener<K extends keyof HTMLEInputElementEventMap>(type: K, listener: (this: HTMLEInputElement, ev: EInputCustomEvent<HTMLEInputElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLEInputElementEventMap>(type: K, listener: (this: HTMLEInputElement, ev: EInputCustomEvent<HTMLEInputElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLEInputElement: {
        prototype: HTMLEInputElement;
        new (): HTMLEInputElement;
    };
    interface HTMLELinkElement extends Components.ELink, HTMLStencilElement {
    }
    var HTMLELinkElement: {
        prototype: HTMLELinkElement;
        new (): HTMLELinkElement;
    };
    interface HTMLEListElement extends Components.EList, HTMLStencilElement {
    }
    var HTMLEListElement: {
        prototype: HTMLEListElement;
        new (): HTMLEListElement;
    };
    interface HTMLEListItemElement extends Components.EListItem, HTMLStencilElement {
    }
    var HTMLEListItemElement: {
        prototype: HTMLEListItemElement;
        new (): HTMLEListItemElement;
    };
    interface HTMLEPillElement extends Components.EPill, HTMLStencilElement {
    }
    var HTMLEPillElement: {
        prototype: HTMLEPillElement;
        new (): HTMLEPillElement;
    };
    interface HTMLESelectElementEventMap {
        "selectChangeEvent": any;
    }
    interface HTMLESelectElement extends Components.ESelect, HTMLStencilElement {
        addEventListener<K extends keyof HTMLESelectElementEventMap>(type: K, listener: (this: HTMLESelectElement, ev: ESelectCustomEvent<HTMLESelectElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLESelectElementEventMap>(type: K, listener: (this: HTMLESelectElement, ev: ESelectCustomEvent<HTMLESelectElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLESelectElement: {
        prototype: HTMLESelectElement;
        new (): HTMLESelectElement;
    };
    interface HTMLESpinnerElement extends Components.ESpinner, HTMLStencilElement {
    }
    var HTMLESpinnerElement: {
        prototype: HTMLESpinnerElement;
        new (): HTMLESpinnerElement;
    };
    interface HTMLETextElement extends Components.EText, HTMLStencilElement {
    }
    var HTMLETextElement: {
        prototype: HTMLETextElement;
        new (): HTMLETextElement;
    };
    interface HTMLETextEditableElementEventMap {
        "editableTextEvent": any;
    }
    interface HTMLETextEditableElement extends Components.ETextEditable, HTMLStencilElement {
        addEventListener<K extends keyof HTMLETextEditableElementEventMap>(type: K, listener: (this: HTMLETextEditableElement, ev: ETextEditableCustomEvent<HTMLETextEditableElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLETextEditableElementEventMap>(type: K, listener: (this: HTMLETextEditableElement, ev: ETextEditableCustomEvent<HTMLETextEditableElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLETextEditableElement: {
        prototype: HTMLETextEditableElement;
        new (): HTMLETextEditableElement;
    };
    interface HTMLLRowElement extends Components.LRow, HTMLStencilElement {
    }
    var HTMLLRowElement: {
        prototype: HTMLLRowElement;
        new (): HTMLLRowElement;
    };
    interface HTMLLSeparatorElement extends Components.LSeparator, HTMLStencilElement {
    }
    var HTMLLSeparatorElement: {
        prototype: HTMLLSeparatorElement;
        new (): HTMLLSeparatorElement;
    };
    interface HTMLLSpacerElement extends Components.LSpacer, HTMLStencilElement {
    }
    var HTMLLSpacerElement: {
        prototype: HTMLLSpacerElement;
        new (): HTMLLSpacerElement;
    };
    interface HTMLPCategoryFormElementEventMap {
        "addSenseQueryCategoryFromModal": any;
        "modalCloseEvent": any;
    }
    /**
     * Component for creating SenseQuery categories
     */
    interface HTMLPCategoryFormElement extends Components.PCategoryForm, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPCategoryFormElementEventMap>(type: K, listener: (this: HTMLPCategoryFormElement, ev: PCategoryFormCustomEvent<HTMLPCategoryFormElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPCategoryFormElementEventMap>(type: K, listener: (this: HTMLPCategoryFormElement, ev: PCategoryFormCustomEvent<HTMLPCategoryFormElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPCategoryFormElement: {
        prototype: HTMLPCategoryFormElement;
        new (): HTMLPCategoryFormElement;
    };
    interface HTMLPDotgridElement extends Components.PDotgrid, HTMLStencilElement {
    }
    var HTMLPDotgridElement: {
        prototype: HTMLPDotgridElement;
        new (): HTMLPDotgridElement;
    };
    interface HTMLPListWithDeleteElementEventMap {
        "listWithDeleteEvent": any;
    }
    interface HTMLPListWithDeleteElement extends Components.PListWithDelete, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPListWithDeleteElementEventMap>(type: K, listener: (this: HTMLPListWithDeleteElement, ev: PListWithDeleteCustomEvent<HTMLPListWithDeleteElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPListWithDeleteElementEventMap>(type: K, listener: (this: HTMLPListWithDeleteElement, ev: PListWithDeleteCustomEvent<HTMLPListWithDeleteElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPListWithDeleteElement: {
        prototype: HTMLPListWithDeleteElement;
        new (): HTMLPListWithDeleteElement;
    };
    interface HTMLPModalElementEventMap {
        "modalCloseEvent": any;
    }
    /**
     * Global modal component that can be used from any part of the webapp
     */
    interface HTMLPModalElement extends Components.PModal, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPModalElementEventMap>(type: K, listener: (this: HTMLPModalElement, ev: PModalCustomEvent<HTMLPModalElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPModalElementEventMap>(type: K, listener: (this: HTMLPModalElement, ev: PModalCustomEvent<HTMLPModalElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPModalElement: {
        prototype: HTMLPModalElement;
        new (): HTMLPModalElement;
    };
    interface HTMLPNotificationElement extends Components.PNotification, HTMLStencilElement {
    }
    var HTMLPNotificationElement: {
        prototype: HTMLPNotificationElement;
        new (): HTMLPNotificationElement;
    };
    interface HTMLPPriorityItemElementEventMap {
        "priorityItemDeleteEvent": any;
        "priorityItemEditEvent": any;
    }
    interface HTMLPPriorityItemElement extends Components.PPriorityItem, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPPriorityItemElementEventMap>(type: K, listener: (this: HTMLPPriorityItemElement, ev: PPriorityItemCustomEvent<HTMLPPriorityItemElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPPriorityItemElementEventMap>(type: K, listener: (this: HTMLPPriorityItemElement, ev: PPriorityItemCustomEvent<HTMLPPriorityItemElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPPriorityItemElement: {
        prototype: HTMLPPriorityItemElement;
        new (): HTMLPPriorityItemElement;
    };
    interface HTMLPPriorityItemFormElementEventMap {
        "addCustomPriorityItem": any;
        "modalCloseEvent": any;
    }
    /**
     * Component for creating and editing priority items
     */
    interface HTMLPPriorityItemFormElement extends Components.PPriorityItemForm, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPPriorityItemFormElementEventMap>(type: K, listener: (this: HTMLPPriorityItemFormElement, ev: PPriorityItemFormCustomEvent<HTMLPPriorityItemFormElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPPriorityItemFormElementEventMap>(type: K, listener: (this: HTMLPPriorityItemFormElement, ev: PPriorityItemFormCustomEvent<HTMLPPriorityItemFormElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPPriorityItemFormElement: {
        prototype: HTMLPPriorityItemFormElement;
        new (): HTMLPPriorityItemFormElement;
    };
    interface HTMLPRespondentDetailFormElementEventMap {
        "addCustomRespondentDetail": any;
    }
    /**
     * Component for creating and editing custom respondent details
     */
    interface HTMLPRespondentDetailFormElement extends Components.PRespondentDetailForm, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPRespondentDetailFormElementEventMap>(type: K, listener: (this: HTMLPRespondentDetailFormElement, ev: PRespondentDetailFormCustomEvent<HTMLPRespondentDetailFormElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPRespondentDetailFormElementEventMap>(type: K, listener: (this: HTMLPRespondentDetailFormElement, ev: PRespondentDetailFormCustomEvent<HTMLPRespondentDetailFormElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPRespondentDetailFormElement: {
        prototype: HTMLPRespondentDetailFormElement;
        new (): HTMLPRespondentDetailFormElement;
    };
    interface HTMLPRespondentDetailItemElementEventMap {
        "respondentDetailDeleteEvent": any;
        "respondentDetailEditEvent": any;
    }
    /**
     * Component for displaying a respondent detail item with edit and delete options
     */
    interface HTMLPRespondentDetailItemElement extends Components.PRespondentDetailItem, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPRespondentDetailItemElementEventMap>(type: K, listener: (this: HTMLPRespondentDetailItemElement, ev: PRespondentDetailItemCustomEvent<HTMLPRespondentDetailItemElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPRespondentDetailItemElementEventMap>(type: K, listener: (this: HTMLPRespondentDetailItemElement, ev: PRespondentDetailItemCustomEvent<HTMLPRespondentDetailItemElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPRespondentDetailItemElement: {
        prototype: HTMLPRespondentDetailItemElement;
        new (): HTMLPRespondentDetailItemElement;
    };
    interface HTMLPTabnavElementEventMap {
        "tabChange": string;
    }
    interface HTMLPTabnavElement extends Components.PTabnav, HTMLStencilElement {
        addEventListener<K extends keyof HTMLPTabnavElementEventMap>(type: K, listener: (this: HTMLPTabnavElement, ev: PTabnavCustomEvent<HTMLPTabnavElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLPTabnavElementEventMap>(type: K, listener: (this: HTMLPTabnavElement, ev: PTabnavCustomEvent<HTMLPTabnavElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLPTabnavElement: {
        prototype: HTMLPTabnavElement;
        new (): HTMLPTabnavElement;
    };
    interface HTMLPTopbarElement extends Components.PTopbar, HTMLStencilElement {
    }
    var HTMLPTopbarElement: {
        prototype: HTMLPTopbarElement;
        new (): HTMLPTopbarElement;
    };
    interface HTMLPUserControlElement extends Components.PUserControl, HTMLStencilElement {
    }
    var HTMLPUserControlElement: {
        prototype: HTMLPUserControlElement;
        new (): HTMLPUserControlElement;
    };
    interface HTMLRespondentSettingsSectionElementEventMap {
        "respondentDetailEditEvent": any;
    }
    interface HTMLRespondentSettingsSectionElement extends Components.RespondentSettingsSection, HTMLStencilElement {
        addEventListener<K extends keyof HTMLRespondentSettingsSectionElementEventMap>(type: K, listener: (this: HTMLRespondentSettingsSectionElement, ev: RespondentSettingsSectionCustomEvent<HTMLRespondentSettingsSectionElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLRespondentSettingsSectionElementEventMap>(type: K, listener: (this: HTMLRespondentSettingsSectionElement, ev: RespondentSettingsSectionCustomEvent<HTMLRespondentSettingsSectionElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLRespondentSettingsSectionElement: {
        prototype: HTMLRespondentSettingsSectionElement;
        new (): HTMLRespondentSettingsSectionElement;
    };
    interface HTMLSensePollSettingsElement extends Components.SensePollSettings, HTMLStencilElement {
    }
    var HTMLSensePollSettingsElement: {
        prototype: HTMLSensePollSettingsElement;
        new (): HTMLSensePollSettingsElement;
    };
    interface HTMLSensePrioritySettingsElement extends Components.SensePrioritySettings, HTMLStencilElement {
    }
    var HTMLSensePrioritySettingsElement: {
        prototype: HTMLSensePrioritySettingsElement;
        new (): HTMLSensePrioritySettingsElement;
    };
    interface HTMLSenseQuerySettingsElement extends Components.SenseQuerySettings, HTMLStencilElement {
    }
    var HTMLSenseQuerySettingsElement: {
        prototype: HTMLSenseQuerySettingsElement;
        new (): HTMLSenseQuerySettingsElement;
    };
    interface HTMLSurveyTypeSettingsSectionElement extends Components.SurveyTypeSettingsSection, HTMLStencilElement {
    }
    var HTMLSurveyTypeSettingsSectionElement: {
        prototype: HTMLSurveyTypeSettingsSectionElement;
        new (): HTMLSurveyTypeSettingsSectionElement;
    };
    interface HTMLVAccountElement extends Components.VAccount, HTMLStencilElement {
    }
    var HTMLVAccountElement: {
        prototype: HTMLVAccountElement;
        new (): HTMLVAccountElement;
    };
    interface HTMLVBillingElement extends Components.VBilling, HTMLStencilElement {
    }
    var HTMLVBillingElement: {
        prototype: HTMLVBillingElement;
        new (): HTMLVBillingElement;
    };
    interface HTMLVCatchAllElement extends Components.VCatchAll, HTMLStencilElement {
    }
    var HTMLVCatchAllElement: {
        prototype: HTMLVCatchAllElement;
        new (): HTMLVCatchAllElement;
    };
    interface HTMLVCheckoutElementEventMap {
        "routeToEvent": any;
    }
    interface HTMLVCheckoutElement extends Components.VCheckout, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVCheckoutElementEventMap>(type: K, listener: (this: HTMLVCheckoutElement, ev: VCheckoutCustomEvent<HTMLVCheckoutElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVCheckoutElementEventMap>(type: K, listener: (this: HTMLVCheckoutElement, ev: VCheckoutCustomEvent<HTMLVCheckoutElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVCheckoutElement: {
        prototype: HTMLVCheckoutElement;
        new (): HTMLVCheckoutElement;
    };
    interface HTMLVCreateSurveyElement extends Components.VCreateSurvey, HTMLStencilElement {
    }
    var HTMLVCreateSurveyElement: {
        prototype: HTMLVCreateSurveyElement;
        new (): HTMLVCreateSurveyElement;
    };
    interface HTMLVDeleteAccountElementEventMap {
        "logoutEvent": any;
    }
    interface HTMLVDeleteAccountElement extends Components.VDeleteAccount, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVDeleteAccountElementEventMap>(type: K, listener: (this: HTMLVDeleteAccountElement, ev: VDeleteAccountCustomEvent<HTMLVDeleteAccountElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVDeleteAccountElementEventMap>(type: K, listener: (this: HTMLVDeleteAccountElement, ev: VDeleteAccountCustomEvent<HTMLVDeleteAccountElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVDeleteAccountElement: {
        prototype: HTMLVDeleteAccountElement;
        new (): HTMLVDeleteAccountElement;
    };
    interface HTMLVDeleteSurveyElement extends Components.VDeleteSurvey, HTMLStencilElement {
    }
    var HTMLVDeleteSurveyElement: {
        prototype: HTMLVDeleteSurveyElement;
        new (): HTMLVDeleteSurveyElement;
    };
    interface HTMLVEditSurveyElement extends Components.VEditSurvey, HTMLStencilElement {
    }
    var HTMLVEditSurveyElement: {
        prototype: HTMLVEditSurveyElement;
        new (): HTMLVEditSurveyElement;
    };
    interface HTMLVEmbedSurveyElement extends Components.VEmbedSurvey, HTMLStencilElement {
    }
    var HTMLVEmbedSurveyElement: {
        prototype: HTMLVEmbedSurveyElement;
        new (): HTMLVEmbedSurveyElement;
    };
    interface HTMLVHomeElementEventMap {
        "routeToEvent": any;
    }
    interface HTMLVHomeElement extends Components.VHome, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVHomeElementEventMap>(type: K, listener: (this: HTMLVHomeElement, ev: VHomeCustomEvent<HTMLVHomeElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVHomeElementEventMap>(type: K, listener: (this: HTMLVHomeElement, ev: VHomeCustomEvent<HTMLVHomeElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVHomeElement: {
        prototype: HTMLVHomeElement;
        new (): HTMLVHomeElement;
    };
    interface HTMLVLoginElementEventMap {
        "authSuccessfulEvent": any;
    }
    /**
     * Login View Component
     * This component provides the user interface for user authentication.
     * It handles user input validation, API communication, and success events.
     * Features:
     * - Email and password input validation
     * - OAuth authentication option
     * - Loading state management
     * - Error handling with user feedback
     * - Responsive design with shadow DOM encapsulation
     * Security Features:
     * - Client-side input validation before API calls
     * - CSRF token handling through ApiWrapper
     * - Secure password input field
     * - Rate limiting protection (handled by backend)
     * User Experience:
     * - Real-time input handling
     * - Loading indicators during authentication
     * - Clear error messages
     * - Links to related pages (signup, password reset)
     * @example <v-login></v-login>
     */
    interface HTMLVLoginElement extends Components.VLogin, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVLoginElementEventMap>(type: K, listener: (this: HTMLVLoginElement, ev: VLoginCustomEvent<HTMLVLoginElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVLoginElementEventMap>(type: K, listener: (this: HTMLVLoginElement, ev: VLoginCustomEvent<HTMLVLoginElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVLoginElement: {
        prototype: HTMLVLoginElement;
        new (): HTMLVLoginElement;
    };
    interface HTMLVPasswordResetElement extends Components.VPasswordReset, HTMLStencilElement {
    }
    var HTMLVPasswordResetElement: {
        prototype: HTMLVPasswordResetElement;
        new (): HTMLVPasswordResetElement;
    };
    interface HTMLVPaymentFailedElement extends Components.VPaymentFailed, HTMLStencilElement {
    }
    var HTMLVPaymentFailedElement: {
        prototype: HTMLVPaymentFailedElement;
        new (): HTMLVPaymentFailedElement;
    };
    interface HTMLVPaymentSuccessElement extends Components.VPaymentSuccess, HTMLStencilElement {
    }
    var HTMLVPaymentSuccessElement: {
        prototype: HTMLVPaymentSuccessElement;
        new (): HTMLVPaymentSuccessElement;
    };
    interface HTMLVPostOauthElementEventMap {
        "authSuccessfulEvent": any;
        "closeModal": any;
        "routeToEvent": any;
    }
    interface HTMLVPostOauthElement extends Components.VPostOauth, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVPostOauthElementEventMap>(type: K, listener: (this: HTMLVPostOauthElement, ev: VPostOauthCustomEvent<HTMLVPostOauthElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVPostOauthElementEventMap>(type: K, listener: (this: HTMLVPostOauthElement, ev: VPostOauthCustomEvent<HTMLVPostOauthElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVPostOauthElement: {
        prototype: HTMLVPostOauthElement;
        new (): HTMLVPostOauthElement;
    };
    interface HTMLVShareSurveyElement extends Components.VShareSurvey, HTMLStencilElement {
    }
    var HTMLVShareSurveyElement: {
        prototype: HTMLVShareSurveyElement;
        new (): HTMLVShareSurveyElement;
    };
    interface HTMLVSharedSurveyElement extends Components.VSharedSurvey, HTMLStencilElement {
    }
    var HTMLVSharedSurveyElement: {
        prototype: HTMLVSharedSurveyElement;
        new (): HTMLVSharedSurveyElement;
    };
    interface HTMLVSignupElementEventMap {
        "authSuccessfulEvent": any;
    }
    interface HTMLVSignupElement extends Components.VSignup, HTMLStencilElement {
        addEventListener<K extends keyof HTMLVSignupElementEventMap>(type: K, listener: (this: HTMLVSignupElement, ev: VSignupCustomEvent<HTMLVSignupElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLVSignupElementEventMap>(type: K, listener: (this: HTMLVSignupElement, ev: VSignupCustomEvent<HTMLVSignupElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLVSignupElement: {
        prototype: HTMLVSignupElement;
        new (): HTMLVSignupElement;
    };
    interface HTMLVSupportElement extends Components.VSupport, HTMLStencilElement {
    }
    var HTMLVSupportElement: {
        prototype: HTMLVSupportElement;
        new (): HTMLVSupportElement;
    };
    interface HTMLVSurveyResultsElement extends Components.VSurveyResults, HTMLStencilElement {
    }
    var HTMLVSurveyResultsElement: {
        prototype: HTMLVSurveyResultsElement;
        new (): HTMLVSurveyResultsElement;
    };
    interface HTMLElementTagNameMap {
        "app-root": HTMLAppRootElement;
        "basic-settings-section": HTMLBasicSettingsSectionElement;
        "c-banner": HTMLCBannerElement;
        "c-card": HTMLCCardElement;
        "c-main": HTMLCMainElement;
        "e-button": HTMLEButtonElement;
        "e-button-oauth": HTMLEButtonOauthElement;
        "e-image": HTMLEImageElement;
        "e-input": HTMLEInputElement;
        "e-link": HTMLELinkElement;
        "e-list": HTMLEListElement;
        "e-list-item": HTMLEListItemElement;
        "e-pill": HTMLEPillElement;
        "e-select": HTMLESelectElement;
        "e-spinner": HTMLESpinnerElement;
        "e-text": HTMLETextElement;
        "e-text-editable": HTMLETextEditableElement;
        "l-row": HTMLLRowElement;
        "l-separator": HTMLLSeparatorElement;
        "l-spacer": HTMLLSpacerElement;
        "p-category-form": HTMLPCategoryFormElement;
        "p-dotgrid": HTMLPDotgridElement;
        "p-list-with-delete": HTMLPListWithDeleteElement;
        "p-modal": HTMLPModalElement;
        "p-notification": HTMLPNotificationElement;
        "p-priority-item": HTMLPPriorityItemElement;
        "p-priority-item-form": HTMLPPriorityItemFormElement;
        "p-respondent-detail-form": HTMLPRespondentDetailFormElement;
        "p-respondent-detail-item": HTMLPRespondentDetailItemElement;
        "p-tabnav": HTMLPTabnavElement;
        "p-topbar": HTMLPTopbarElement;
        "p-user-control": HTMLPUserControlElement;
        "respondent-settings-section": HTMLRespondentSettingsSectionElement;
        "sense-poll-settings": HTMLSensePollSettingsElement;
        "sense-priority-settings": HTMLSensePrioritySettingsElement;
        "sense-query-settings": HTMLSenseQuerySettingsElement;
        "survey-type-settings-section": HTMLSurveyTypeSettingsSectionElement;
        "v-account": HTMLVAccountElement;
        "v-billing": HTMLVBillingElement;
        "v-catch-all": HTMLVCatchAllElement;
        "v-checkout": HTMLVCheckoutElement;
        "v-create-survey": HTMLVCreateSurveyElement;
        "v-delete-account": HTMLVDeleteAccountElement;
        "v-delete-survey": HTMLVDeleteSurveyElement;
        "v-edit-survey": HTMLVEditSurveyElement;
        "v-embed-survey": HTMLVEmbedSurveyElement;
        "v-home": HTMLVHomeElement;
        "v-login": HTMLVLoginElement;
        "v-password-reset": HTMLVPasswordResetElement;
        "v-payment-failed": HTMLVPaymentFailedElement;
        "v-payment-success": HTMLVPaymentSuccessElement;
        "v-post-oauth": HTMLVPostOauthElement;
        "v-share-survey": HTMLVShareSurveyElement;
        "v-shared-survey": HTMLVSharedSurveyElement;
        "v-signup": HTMLVSignupElement;
        "v-support": HTMLVSupportElement;
        "v-survey-results": HTMLVSurveyResultsElement;
    }
}
declare namespace LocalJSX {
    interface AppRoot {
    }
    interface BasicSettingsSection {
        "survey"?: any;
        "surveyId"?: string;
    }
    interface CBanner {
        "position"?: string;
        "theme"?: string;
    }
    interface CCard {
        "clickable"?: boolean;
    }
    interface CMain {
        "variant"?: string;
    }
    interface EButton {
        "action"?: string;
        "active"?: boolean;
        "disabled"?: boolean;
        "onButtonClickEvent"?: (event: EButtonCustomEvent<any>) => void;
        "size"?: string;
        "theme"?: string;
        "value"?: any;
        "variant"?: string;
    }
    interface EButtonOauth {
        "onRouteToEvent"?: (event: EButtonOauthCustomEvent<any>) => void;
        "variant"?: string;
    }
    interface EImage {
        "src"?: string;
        "variant"?: string;
        "width"?: string;
    }
    interface EInput {
        "checked"?: boolean;
        "label"?: string;
        "name"?: string;
        "onInputEvent"?: (event: EInputCustomEvent<any>) => void;
        "placeholder"?: string;
        "type"?: string;
        "value"?: string;
    }
    interface ELink {
        "active"?: boolean;
        "theme"?: string;
        "url"?: string;
        "variant"?: string;
    }
    interface EList {
    }
    interface EListItem {
    }
    interface EPill {
        "color"?: string;
    }
    interface ESelect {
        "name"?: string;
        "onSelectChangeEvent"?: (event: ESelectCustomEvent<any>) => void;
        "options"?: any;
        "resetTrigger"?: number;
    }
    interface ESpinner {
        "theme"?: string;
    }
    interface EText {
        "theme"?: string;
        "variant"?: string;
        "weight"?: string;
    }
    interface ETextEditable {
        "active"?: boolean;
        "attribute"?: string;
        "bypass"?: boolean;
        "entity"?: string;
        "label"?: string;
        "onEditableTextEvent"?: (event: ETextEditableCustomEvent<any>) => void;
        "type"?: string;
        "value"?: string;
    }
    interface LRow {
        "align"?: string;
        "direction"?: string;
        "justifyContent"?: string;
        "variant"?: string;
    }
    interface LSeparator {
        "variant"?: string;
    }
    interface LSpacer {
        "value"?: number;
        "variant"?: string;
    }
    /**
     * Component for creating SenseQuery categories
     */
    interface PCategoryForm {
        "onAddSenseQueryCategoryFromModal"?: (event: PCategoryFormCustomEvent<any>) => void;
        "onModalCloseEvent"?: (event: PCategoryFormCustomEvent<any>) => void;
    }
    interface PDotgrid {
        "height"?: string;
        "width"?: string;
    }
    interface PListWithDelete {
        "emptyMessage"?: string;
        "items"?: string;
        "name"?: string;
        "onListWithDeleteEvent"?: (event: PListWithDeleteCustomEvent<any>) => void;
    }
    /**
     * Global modal component that can be used from any part of the webapp
     */
    interface PModal {
        "closeOnBackdropClick"?: boolean;
        "isOpen"?: boolean;
        "modalTitle"?: string;
        "onModalCloseEvent"?: (event: PModalCustomEvent<any>) => void;
    }
    interface PNotification {
        "duration"?: number;
        "message"?: string;
        "theme"?: 'success' | 'danger' | 'info';
    }
    interface PPriorityItem {
        "index"?: number;
        "item"?: string;
        "onPriorityItemDeleteEvent"?: (event: PPriorityItemCustomEvent<any>) => void;
        "onPriorityItemEditEvent"?: (event: PPriorityItemCustomEvent<any>) => void;
    }
    /**
     * Component for creating and editing priority items
     */
    interface PPriorityItemForm {
        "editingItem"?: string;
        "isEditMode"?: boolean;
        "onAddCustomPriorityItem"?: (event: PPriorityItemFormCustomEvent<any>) => void;
        "onModalCloseEvent"?: (event: PPriorityItemFormCustomEvent<any>) => void;
    }
    /**
     * Component for creating and editing custom respondent details
     */
    interface PRespondentDetailForm {
        "editingDetail"?: string;
        "isEditMode"?: boolean;
        "onAddCustomRespondentDetail"?: (event: PRespondentDetailFormCustomEvent<any>) => void;
    }
    /**
     * Component for displaying a respondent detail item with edit and delete options
     */
    interface PRespondentDetailItem {
        "detail"?: string;
        "index"?: number;
        "onRespondentDetailDeleteEvent"?: (event: PRespondentDetailItemCustomEvent<any>) => void;
        "onRespondentDetailEditEvent"?: (event: PRespondentDetailItemCustomEvent<any>) => void;
    }
    interface PTabnav {
        "activeTab"?: string;
        "onTabChange"?: (event: PTabnavCustomEvent<string>) => void;
        "tabs"?: TabItem[];
    }
    interface PTopbar {
    }
    interface PUserControl {
    }
    interface RespondentSettingsSection {
        "onRespondentDetailEditEvent"?: (event: RespondentSettingsSectionCustomEvent<any>) => void;
        "survey"?: any;
        "surveyId"?: string;
    }
    interface SensePollSettings {
        "survey"?: any;
        "surveyId"?: string;
    }
    interface SensePrioritySettings {
        "survey"?: any;
        "surveyId"?: string;
    }
    interface SenseQuerySettings {
        "survey"?: any;
        "surveyId"?: string;
    }
    interface SurveyTypeSettingsSection {
        "survey"?: any;
        "surveyId"?: string;
    }
    interface VAccount {
    }
    interface VBilling {
    }
    interface VCatchAll {
    }
    interface VCheckout {
        "onRouteToEvent"?: (event: VCheckoutCustomEvent<any>) => void;
        "orderId"?: string;
    }
    interface VCreateSurvey {
    }
    interface VDeleteAccount {
        "onLogoutEvent"?: (event: VDeleteAccountCustomEvent<any>) => void;
    }
    interface VDeleteSurvey {
        "surveyId"?: string;
    }
    interface VEditSurvey {
        "surveyId"?: string;
    }
    interface VEmbedSurvey {
        "surveyId"?: string;
    }
    interface VHome {
        "onRouteToEvent"?: (event: VHomeCustomEvent<any>) => void;
    }
    /**
     * Login View Component
     * This component provides the user interface for user authentication.
     * It handles user input validation, API communication, and success events.
     * Features:
     * - Email and password input validation
     * - OAuth authentication option
     * - Loading state management
     * - Error handling with user feedback
     * - Responsive design with shadow DOM encapsulation
     * Security Features:
     * - Client-side input validation before API calls
     * - CSRF token handling through ApiWrapper
     * - Secure password input field
     * - Rate limiting protection (handled by backend)
     * User Experience:
     * - Real-time input handling
     * - Loading indicators during authentication
     * - Clear error messages
     * - Links to related pages (signup, password reset)
     * @example <v-login></v-login>
     */
    interface VLogin {
        /**
          * Event emitted when authentication is successful Bubbles up to parent components to trigger session initialization
         */
        "onAuthSuccessfulEvent"?: (event: VLoginCustomEvent<any>) => void;
    }
    interface VPasswordReset {
    }
    interface VPaymentFailed {
        "sessionId"?: string;
    }
    interface VPaymentSuccess {
        "sessionId"?: string;
    }
    interface VPostOauth {
        "onAuthSuccessfulEvent"?: (event: VPostOauthCustomEvent<any>) => void;
        "onCloseModal"?: (event: VPostOauthCustomEvent<any>) => void;
        "onRouteToEvent"?: (event: VPostOauthCustomEvent<any>) => void;
        "provider"?: string;
    }
    interface VShareSurvey {
        "surveyId"?: string;
    }
    interface VSharedSurvey {
        "shareKey"?: string;
    }
    interface VSignup {
        "onAuthSuccessfulEvent"?: (event: VSignupCustomEvent<any>) => void;
    }
    interface VSupport {
    }
    interface VSurveyResults {
        "surveyId"?: string;
    }
    interface IntrinsicElements {
        "app-root": AppRoot;
        "basic-settings-section": BasicSettingsSection;
        "c-banner": CBanner;
        "c-card": CCard;
        "c-main": CMain;
        "e-button": EButton;
        "e-button-oauth": EButtonOauth;
        "e-image": EImage;
        "e-input": EInput;
        "e-link": ELink;
        "e-list": EList;
        "e-list-item": EListItem;
        "e-pill": EPill;
        "e-select": ESelect;
        "e-spinner": ESpinner;
        "e-text": EText;
        "e-text-editable": ETextEditable;
        "l-row": LRow;
        "l-separator": LSeparator;
        "l-spacer": LSpacer;
        "p-category-form": PCategoryForm;
        "p-dotgrid": PDotgrid;
        "p-list-with-delete": PListWithDelete;
        "p-modal": PModal;
        "p-notification": PNotification;
        "p-priority-item": PPriorityItem;
        "p-priority-item-form": PPriorityItemForm;
        "p-respondent-detail-form": PRespondentDetailForm;
        "p-respondent-detail-item": PRespondentDetailItem;
        "p-tabnav": PTabnav;
        "p-topbar": PTopbar;
        "p-user-control": PUserControl;
        "respondent-settings-section": RespondentSettingsSection;
        "sense-poll-settings": SensePollSettings;
        "sense-priority-settings": SensePrioritySettings;
        "sense-query-settings": SenseQuerySettings;
        "survey-type-settings-section": SurveyTypeSettingsSection;
        "v-account": VAccount;
        "v-billing": VBilling;
        "v-catch-all": VCatchAll;
        "v-checkout": VCheckout;
        "v-create-survey": VCreateSurvey;
        "v-delete-account": VDeleteAccount;
        "v-delete-survey": VDeleteSurvey;
        "v-edit-survey": VEditSurvey;
        "v-embed-survey": VEmbedSurvey;
        "v-home": VHome;
        "v-login": VLogin;
        "v-password-reset": VPasswordReset;
        "v-payment-failed": VPaymentFailed;
        "v-payment-success": VPaymentSuccess;
        "v-post-oauth": VPostOauth;
        "v-share-survey": VShareSurvey;
        "v-shared-survey": VSharedSurvey;
        "v-signup": VSignup;
        "v-support": VSupport;
        "v-survey-results": VSurveyResults;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "app-root": LocalJSX.AppRoot & JSXBase.HTMLAttributes<HTMLAppRootElement>;
            "basic-settings-section": LocalJSX.BasicSettingsSection & JSXBase.HTMLAttributes<HTMLBasicSettingsSectionElement>;
            "c-banner": LocalJSX.CBanner & JSXBase.HTMLAttributes<HTMLCBannerElement>;
            "c-card": LocalJSX.CCard & JSXBase.HTMLAttributes<HTMLCCardElement>;
            "c-main": LocalJSX.CMain & JSXBase.HTMLAttributes<HTMLCMainElement>;
            "e-button": LocalJSX.EButton & JSXBase.HTMLAttributes<HTMLEButtonElement>;
            "e-button-oauth": LocalJSX.EButtonOauth & JSXBase.HTMLAttributes<HTMLEButtonOauthElement>;
            "e-image": LocalJSX.EImage & JSXBase.HTMLAttributes<HTMLEImageElement>;
            "e-input": LocalJSX.EInput & JSXBase.HTMLAttributes<HTMLEInputElement>;
            "e-link": LocalJSX.ELink & JSXBase.HTMLAttributes<HTMLELinkElement>;
            "e-list": LocalJSX.EList & JSXBase.HTMLAttributes<HTMLEListElement>;
            "e-list-item": LocalJSX.EListItem & JSXBase.HTMLAttributes<HTMLEListItemElement>;
            "e-pill": LocalJSX.EPill & JSXBase.HTMLAttributes<HTMLEPillElement>;
            "e-select": LocalJSX.ESelect & JSXBase.HTMLAttributes<HTMLESelectElement>;
            "e-spinner": LocalJSX.ESpinner & JSXBase.HTMLAttributes<HTMLESpinnerElement>;
            "e-text": LocalJSX.EText & JSXBase.HTMLAttributes<HTMLETextElement>;
            "e-text-editable": LocalJSX.ETextEditable & JSXBase.HTMLAttributes<HTMLETextEditableElement>;
            "l-row": LocalJSX.LRow & JSXBase.HTMLAttributes<HTMLLRowElement>;
            "l-separator": LocalJSX.LSeparator & JSXBase.HTMLAttributes<HTMLLSeparatorElement>;
            "l-spacer": LocalJSX.LSpacer & JSXBase.HTMLAttributes<HTMLLSpacerElement>;
            /**
             * Component for creating SenseQuery categories
             */
            "p-category-form": LocalJSX.PCategoryForm & JSXBase.HTMLAttributes<HTMLPCategoryFormElement>;
            "p-dotgrid": LocalJSX.PDotgrid & JSXBase.HTMLAttributes<HTMLPDotgridElement>;
            "p-list-with-delete": LocalJSX.PListWithDelete & JSXBase.HTMLAttributes<HTMLPListWithDeleteElement>;
            /**
             * Global modal component that can be used from any part of the webapp
             */
            "p-modal": LocalJSX.PModal & JSXBase.HTMLAttributes<HTMLPModalElement>;
            "p-notification": LocalJSX.PNotification & JSXBase.HTMLAttributes<HTMLPNotificationElement>;
            "p-priority-item": LocalJSX.PPriorityItem & JSXBase.HTMLAttributes<HTMLPPriorityItemElement>;
            /**
             * Component for creating and editing priority items
             */
            "p-priority-item-form": LocalJSX.PPriorityItemForm & JSXBase.HTMLAttributes<HTMLPPriorityItemFormElement>;
            /**
             * Component for creating and editing custom respondent details
             */
            "p-respondent-detail-form": LocalJSX.PRespondentDetailForm & JSXBase.HTMLAttributes<HTMLPRespondentDetailFormElement>;
            /**
             * Component for displaying a respondent detail item with edit and delete options
             */
            "p-respondent-detail-item": LocalJSX.PRespondentDetailItem & JSXBase.HTMLAttributes<HTMLPRespondentDetailItemElement>;
            "p-tabnav": LocalJSX.PTabnav & JSXBase.HTMLAttributes<HTMLPTabnavElement>;
            "p-topbar": LocalJSX.PTopbar & JSXBase.HTMLAttributes<HTMLPTopbarElement>;
            "p-user-control": LocalJSX.PUserControl & JSXBase.HTMLAttributes<HTMLPUserControlElement>;
            "respondent-settings-section": LocalJSX.RespondentSettingsSection & JSXBase.HTMLAttributes<HTMLRespondentSettingsSectionElement>;
            "sense-poll-settings": LocalJSX.SensePollSettings & JSXBase.HTMLAttributes<HTMLSensePollSettingsElement>;
            "sense-priority-settings": LocalJSX.SensePrioritySettings & JSXBase.HTMLAttributes<HTMLSensePrioritySettingsElement>;
            "sense-query-settings": LocalJSX.SenseQuerySettings & JSXBase.HTMLAttributes<HTMLSenseQuerySettingsElement>;
            "survey-type-settings-section": LocalJSX.SurveyTypeSettingsSection & JSXBase.HTMLAttributes<HTMLSurveyTypeSettingsSectionElement>;
            "v-account": LocalJSX.VAccount & JSXBase.HTMLAttributes<HTMLVAccountElement>;
            "v-billing": LocalJSX.VBilling & JSXBase.HTMLAttributes<HTMLVBillingElement>;
            "v-catch-all": LocalJSX.VCatchAll & JSXBase.HTMLAttributes<HTMLVCatchAllElement>;
            "v-checkout": LocalJSX.VCheckout & JSXBase.HTMLAttributes<HTMLVCheckoutElement>;
            "v-create-survey": LocalJSX.VCreateSurvey & JSXBase.HTMLAttributes<HTMLVCreateSurveyElement>;
            "v-delete-account": LocalJSX.VDeleteAccount & JSXBase.HTMLAttributes<HTMLVDeleteAccountElement>;
            "v-delete-survey": LocalJSX.VDeleteSurvey & JSXBase.HTMLAttributes<HTMLVDeleteSurveyElement>;
            "v-edit-survey": LocalJSX.VEditSurvey & JSXBase.HTMLAttributes<HTMLVEditSurveyElement>;
            "v-embed-survey": LocalJSX.VEmbedSurvey & JSXBase.HTMLAttributes<HTMLVEmbedSurveyElement>;
            "v-home": LocalJSX.VHome & JSXBase.HTMLAttributes<HTMLVHomeElement>;
            /**
             * Login View Component
             * This component provides the user interface for user authentication.
             * It handles user input validation, API communication, and success events.
             * Features:
             * - Email and password input validation
             * - OAuth authentication option
             * - Loading state management
             * - Error handling with user feedback
             * - Responsive design with shadow DOM encapsulation
             * Security Features:
             * - Client-side input validation before API calls
             * - CSRF token handling through ApiWrapper
             * - Secure password input field
             * - Rate limiting protection (handled by backend)
             * User Experience:
             * - Real-time input handling
             * - Loading indicators during authentication
             * - Clear error messages
             * - Links to related pages (signup, password reset)
             * @example <v-login></v-login>
             */
            "v-login": LocalJSX.VLogin & JSXBase.HTMLAttributes<HTMLVLoginElement>;
            "v-password-reset": LocalJSX.VPasswordReset & JSXBase.HTMLAttributes<HTMLVPasswordResetElement>;
            "v-payment-failed": LocalJSX.VPaymentFailed & JSXBase.HTMLAttributes<HTMLVPaymentFailedElement>;
            "v-payment-success": LocalJSX.VPaymentSuccess & JSXBase.HTMLAttributes<HTMLVPaymentSuccessElement>;
            "v-post-oauth": LocalJSX.VPostOauth & JSXBase.HTMLAttributes<HTMLVPostOauthElement>;
            "v-share-survey": LocalJSX.VShareSurvey & JSXBase.HTMLAttributes<HTMLVShareSurveyElement>;
            "v-shared-survey": LocalJSX.VSharedSurvey & JSXBase.HTMLAttributes<HTMLVSharedSurveyElement>;
            "v-signup": LocalJSX.VSignup & JSXBase.HTMLAttributes<HTMLVSignupElement>;
            "v-support": LocalJSX.VSupport & JSXBase.HTMLAttributes<HTMLVSupportElement>;
            "v-survey-results": LocalJSX.VSurveyResults & JSXBase.HTMLAttributes<HTMLVSurveyResultsElement>;
        }
    }
}
