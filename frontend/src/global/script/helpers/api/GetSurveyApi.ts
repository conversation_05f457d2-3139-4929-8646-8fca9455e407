import { Var, FrontendLogger } from '../../var';
import { ApiWrapper } from './ApiWrapper';

/**
 * Fetches a specific survey by ID
 *
 * This function retrieves survey data from the server using the survey ID.
 * It includes authentication and comprehensive error handling.
 *
 * @param surveyId - The unique identifier of the survey to fetch
 * @returns Promise resolving to survey data or error information
 *
 * @example
 * const result = await GetSurveyApi('survey-123');
 * if (result.success) {
 *   console.log('Survey data:', result.payload);
 * }
 */
export const GetSurveyApi = async (surveyId: string) => {
  try {
    // DEBUG: Log survey fetch attempt
    FrontendLogger.debug('Fetching survey data', {
      surveyId,
      endpoint: `${Var.api.endpoint.surveys}/${surveyId}`,
    });

    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}`, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    // DEBUG: Log survey fetch result
    FrontendLogger.debug('Survey fetch completed', {
      surveyId,
      success: result.success,
      hasPayload: !!result.payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log survey fetch error
    FrontendLogger.error('Error fetching survey', {
      surveyId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Error fetching survey',
      payload: null,
    };
  }
};
