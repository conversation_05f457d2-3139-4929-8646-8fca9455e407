import { Var, FrontendLogger } from '../../var';
import { ApiWrapper } from './ApiWrapper';

/**
 * Checks the current session status with the server
 *
 * This function verifies if the user's session is still active and valid.
 * It's used during application initialization and for periodic session validation.
 *
 * @returns Promise resolving to session status information
 *
 * @example
 * const result = await CheckSessionStatusApi();
 * if (result.success && result.payload.isSessionActive) {
 *   // Session is active
 * }
 */
export const CheckSessionStatusApi = async () => {
  try {
    // DEBUG: Log session check initiation
    FrontendLogger.debug('Checking session status', {
      endpoint: Var.api.endpoint.account.auth.sessionCheck,
    });

    const result = await ApiWrapper(Var.api.endpoint.account.auth.sessionCheck, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for consistency
    });

    // DEBUG: Log session check result
    FrontendLogger.debug('Session status check completed', {
      success: result.success,
      isSessionActive: result.payload?.isSessionActive,
      hasMessage: !!result.message,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log session check error
    FrontendLogger.error('Error checking session status', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });

    return {
      success: false,
      message: 'Failed to check session status',
      payload: null,
    };
  }
};
