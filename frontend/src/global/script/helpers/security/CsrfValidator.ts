import { Store } from '../../store';

/**
 * CSRF Token Validator Utility Class
 *
 * This class provides static methods for validating, managing, and inspecting
 * CSRF (Cross-Site Request Forgery) tokens used for API security.
 *
 * CSRF tokens are used to prevent cross-site request forgery attacks by
 * ensuring that requests originate from the legitimate application.
 *
 * Security Features:
 * - Token presence validation
 * - Token format validation (length and character set)
 * - Safe token information extraction for debugging
 * - Secure token clearing functionality
 *
 * @example
 * // Check if current token is valid
 * if (CsrfValidator.isTokenValid()) {
 *   // Proceed with API request
 * }
 *
 * // Get token information for debugging
 * const info = CsrfValidator.getTokenInfo();
 * console.log(`Token status: ${info.hasToken ? 'valid' : 'invalid'}`);
 */
export class CsrfValidator {
  /**
   * Checks if the current CSRF token is valid (present and non-empty)
   *
   * This is a basic validation that only checks for token presence.
   * For format validation, use isTokenFormatValid().
   *
   * @returns true if token exists and is non-empty, false otherwise
   *
   * @example
   * if (CsrfValidator.isTokenValid()) {
   *   // Token is present, safe to make API requests
   * }
   */
  static isTokenValid(): boolean {
    return !!Store.csrfToken && Store.csrfToken.length > 0;
  }

  /**
   * Clears the current CSRF token from the store
   *
   * This method is typically called during logout or when a token
   * becomes invalid. It ensures the token is completely removed
   * from client-side storage.
   *
   * @example
   * // Clear token on logout
   * CsrfValidator.clearToken();
   */
  static clearToken(): void {
    Store.csrfToken = '';
  }

  /**
   * Gets safe information about the current CSRF token for debugging
   *
   * This method provides token information without exposing the actual
   * token value. It's safe to use in logs and debugging output.
   *
   * @returns Object containing token status, safe prefix, and length
   *
   * @example
   * const info = CsrfValidator.getTokenInfo();
   * console.log(`Token: ${info.tokenPrefix}, Length: ${info.tokenLength}`);
   */
  static getTokenInfo(): { hasToken: boolean; tokenPrefix: string; tokenLength: number } {
    const hasToken = this.isTokenValid();
    return {
      hasToken,
      tokenPrefix: hasToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
      tokenLength: Store.csrfToken.length,
    };
  }

  /**
   * Validates the format and structure of the current CSRF token
   *
   * This method performs comprehensive validation of the token format:
   * - Length validation (32-256 characters)
   * - Character set validation (base64-like characters)
   *
   * This is more thorough than isTokenValid() and should be used
   * when you need to ensure the token is properly formatted.
   *
   * @returns true if token format is valid, false otherwise
   *
   * @example
   * if (CsrfValidator.isTokenFormatValid()) {
   *   // Token is properly formatted
   * } else {
   *   // Token may be corrupted, fetch a new one
   * }
   */
  static isTokenFormatValid(): boolean {
    if (!Store.csrfToken) return false;

    const token = Store.csrfToken;
    // Validate token length (typical CSRF tokens are 32-256 characters)
    const isValidLength = token.length >= 32 && token.length <= 256;
    // Validate token format (base64-like characters plus URL-safe variants)
    const isValidFormat = /^[a-zA-Z0-9+/=_-]+$/.test(token);

    return isValidLength && isValidFormat;
  }
}
