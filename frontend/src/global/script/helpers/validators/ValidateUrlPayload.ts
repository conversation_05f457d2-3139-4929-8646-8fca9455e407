import Joi from "joi";

const ValidateUrlSchema = Joi.object({
  url: Joi.string()
    .uri({ scheme: ["https", "http"] })
    .regex(
      /^(https?:\/\/)?(localhost|127\.0\.0\.1)(:[0-9]+)?(\/.*)?$|^https:\/\/.*$/
    )
    .allow(""),
});

export const ValidateUrlPayload = (payload: any) => {
  let { error } = ValidateUrlSchema.validate(payload);

  if (error) {
    return {
      isValid: false,
      validationMessage: `❌ ${error.details[0].message}`,
    };
  } else {
    return { isValid: true, validationMessage: "" };
  }
};
