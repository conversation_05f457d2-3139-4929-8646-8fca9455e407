import { MailPayloadInterface } from '../../interfaces';

/**
 * Generates a standardized mail payload for email operations
 *
 * This function creates a properly formatted payload for sending emails
 * through the mail API. It handles email normalization and ensures
 * consistent data structure across the application.
 *
 * Email Processing:
 * - Trims whitespace from email addresses
 * - Converts email to lowercase for consistency
 * - Maintains original variant specification
 *
 * Supported Variants:
 * - 'emailVerificationCode': For email verification during signup
 * - 'passwordResetCode': For password reset requests
 * - Other custom variants as defined by the backend
 *
 * @param email - The recipient email address (will be normalized)
 * @param variant - The type of email to send (e.g., 'emailVerificationCode')
 * @returns Formatted mail payload ready for API submission
 *
 * @example
 * // Generate email verification payload
 * const payload = GenerateMailPayload('<EMAIL>', 'emailVerificationCode');
 * // Returns: { email: '<EMAIL>', variant: 'emailVerificationCode' }
 *
 * @example
 * // Generate password reset payload
 * const payload = GenerateMailPayload(' <EMAIL> ', 'passwordResetCode');
 * // Returns: { email: '<EMAIL>', variant: 'passwordResetCode' }
 */
export const GenerateMailPayload = (email: string, variant: string): MailPayloadInterface => {
  // Create standardized mail payload with normalized email
  const mailCodePayload: MailPayloadInterface = {
    email: email.trim().toLowerCase(), // Normalize email: trim whitespace and convert to lowercase
    variant: variant, // Preserve original variant specification
  };

  return mailCodePayload;
};
