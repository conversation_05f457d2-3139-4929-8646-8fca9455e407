import { Component, Event, EventEmitter, State, Host, Listen, h } from '@stencil/core';
import { loginApi, generateLoginPayload, validateLoginPayload } from './helpers';
import { loginPayloadInterface } from './interfaces';
import { Var, FrontendLogger } from '../../../../global/script/var';
import { Store } from '../../../../global/script/store';

/**
 * Login View Component
 *
 * This component provides the user interface for user authentication.
 * It handles user input validation, API communication, and success events.
 *
 * Features:
 * - Email and password input validation
 * - OAuth authentication option
 * - Loading state management
 * - Error handling with user feedback
 * - Responsive design with shadow DOM encapsulation
 *
 * Security Features:
 * - Client-side input validation before API calls
 * - CSRF token handling through ApiWrapper
 * - Secure password input field
 * - Rate limiting protection (handled by backend)
 *
 * User Experience:
 * - Real-time input handling
 * - Loading indicators during authentication
 * - Clear error messages
 * - Links to related pages (signup, password reset)
 *
 * @example
 * <v-login></v-login>
 */
@Component({
  tag: 'v-login',
  styleUrl: 'v-login.css',
  shadow: true,
})
export class VLogin {
  /**
   * Event emitted when authentication is successful
   * Bubbles up to parent components to trigger session initialization
   */
  @Event({
    eventName: 'authSuccessfulEvent',
    bubbles: true,
  })
  authSuccessfulEventEmitter: EventEmitter;

  /**
   * Handles button click events from child components
   * Currently handles the login action trigger
   */
  @Listen('buttonClickEvent') handleButtonClickEvent(e) {
    // DEBUG: Log button click events
    FrontendLogger.debug('Login view button click event', {
      action: e.detail.action,
      hasDetail: !!e.detail,
    });

    if (e.detail.action === 'loginUser') {
      this.loginUser();
    }
  }

  /**
   * Handles input events from form fields
   * Updates component state with user input
   */
  @Listen('inputEvent') handleInputEvent(e) {
    // DEBUG: Log input events (without sensitive data)
    FrontendLogger.debug('Login view input event', {
      fieldName: e.detail.name,
      hasValue: !!e.detail.value,
      valueLength: e.detail.value?.length || 0,
    });

    if (e.detail.name === 'email') {
      this.email = e.detail.value;
    } else if (e.detail.name === 'password') {
      this.password = e.detail.value;
    }
  }

  /** Loading state indicator for login process */
  @State() isLoggingIn: boolean = false;

  /** User's email input (private to prevent external access) */
  private email: string = '';

  /** User's password input (private to prevent external access) */
  private password: string = '';

  /**
   * Handles the user login process
   *
   * This method orchestrates the complete login flow:
   * 1. Generates and validates the login payload
   * 2. Calls the login API with proper error handling
   * 3. Manages loading states for user feedback
   * 4. Emits success event to trigger session initialization
   *
   * Security considerations:
   * - Client-side validation before API call
   * - Sensitive data is not logged
   * - Loading state prevents multiple simultaneous requests
   *
   * @returns Promise<void> - Completes when login process finishes
   */
  async loginUser() {
    // DEBUG: Log login attempt (without sensitive data)
    FrontendLogger.debug('Login attempt initiated', {
      hasEmail: !!this.email,
      emailLength: this.email.length,
      hasPassword: !!this.password,
      passwordLength: this.password.length,
    });

    // Generate login payload from user input
    let loginPayload: loginPayloadInterface = generateLoginPayload(this.email, this.password);

    // Validate payload before sending to server
    let { isValid, validationMessage } = validateLoginPayload(loginPayload);
    if (!isValid) {
      FrontendLogger.warn('Login validation failed', {
        validationMessage,
        hasEmail: !!this.email,
        hasPassword: !!this.password,
      });
      return alert(validationMessage);
    }

    // Set loading state to prevent multiple requests and show user feedback
    this.isLoggingIn = true;
    FrontendLogger.debug('Login API call starting');

    try {
      // Call login API with validated payload
      let { success, message } = await loginApi(loginPayload);

      // DEBUG: Log API response (without sensitive data)
      FrontendLogger.debug('Login API response received', {
        success,
        hasMessage: !!message,
      });

      if (!success) {
        FrontendLogger.warn('Login failed', { message });
        return alert(message);
      }

      // Login successful - emit event to trigger session initialization
      FrontendLogger.debug('Login successful, emitting auth success event');
      this.authSuccessfulEventEmitter.emit();
    } catch (error) {
      // Handle unexpected errors
      FrontendLogger.error('Unexpected error during login', {
        error: error instanceof Error ? error.message : String(error),
      });
      alert('An unexpected error occurred during login. Please try again.');
    } finally {
      // Always clear loading state
      this.isLoggingIn = false;
    }
  }

  /**
   * Component lifecycle method called before the component loads
   *
   * Sets up the component state and page metadata:
   * - Updates global store with current view
   * - Sets appropriate page title for SEO and user experience
   */
  componentWillLoad() {
    // DEBUG: Log component initialization
    FrontendLogger.debug('Login component initializing', {
      currentActiveView: Store.activeView,
      currentTitle: document.title,
    });

    // Update global store to track current view
    Store.activeView = 'login';

    // Set page title for browser tab and SEO
    document.title = 'Login | Sensefolks';
  }

  render() {
    return (
      <Host>
        <div class="form">
          <e-image src={Var.app.branding.logo.logotype.withoutBg} width="180px"></e-image>
          <l-spacer value={1}></l-spacer>
          <p-dotgrid height="50px" width="100%"></p-dotgrid>
          <l-spacer value={2}></l-spacer>
          <e-text variant="display">Log in</e-text>
          <l-spacer value={0.75}></l-spacer>
          <l-row justifyContent="flex-start"></l-row>
          <e-text>
            Don't have an account? <e-link url="/signup">Sign up</e-link>
          </e-text>
          <l-spacer value={2}></l-spacer>
          <e-button-oauth></e-button-oauth>
          <l-spacer value={2.5}></l-spacer>
          <l-separator variant="oauth"></l-separator>
          <l-spacer value={2.5}></l-spacer>
          <e-input type="email" name="email" placeholder="Email"></e-input>
          <l-spacer value={2}></l-spacer>
          <e-input
            type="password"
            name="password"
            placeholder="Password (Min. 8 letters)"
          ></e-input>
          <l-spacer value={2}></l-spacer>
          <e-button action="loginUser" active={this.isLoggingIn} size="wide">
            Log in
          </e-button>
          <l-spacer value={1}></l-spacer>
          <e-link url="/password-reset">Reset password</e-link>
          <l-spacer value={2}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <e-text variant="footnote">
            By logging in, you accept our{' '}
            <e-link variant="externalLink" url={Var.app.policy.tos.url}>
              terms of service
            </e-link>{' '}
            &{' '}
            <e-link variant="externalLink" url={Var.app.policy.privacy.url}>
              privacy policy
            </e-link>
          </e-text>
        </div>
      </Host>
    );
  }
}
