import { googleProfilePayloadInterface } from '../../interfaces';
import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const googleProfileApi = async (payload: googleProfilePayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.auth.oauth.google, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error with Google OAuth:', error);
    return {
      success: false,
      message: 'Error with Google OAuth',
      payload: null,
    };
  }
};
