import {
  Component,
  Event,
  EventEmitter,
  State,
  Host,
  Listen,
  h,
} from "@stencil/core";
import {
  signupApi,
  generateSignupPayload,
  validateSignupPayload,
} from "./helpers";
import { signupPayloadInterface } from "./interfaces";
import { Var } from "../../../../global/script/var";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-signup",
  styleUrl: "v-signup.css",
  shadow: true,
})
export class VSignup {
  @Event({
    eventName: "authSuccessfulEvent",
    bubbles: true,
  })
  authSuccessfulEventEmitter: EventEmitter;

  @Listen("buttonClickEvent") handleButtonClickEvent(e) {
    if (e.detail.action === "signupUser") {
      this.signupUser();
    }
  }

  @Listen("inputEvent") handleInputEvent(e) {
    if (e.detail.name === "name") {
      this.name = e.detail.value;
    } else if (e.detail.name === "email") {
      this.email = e.detail.value;
    } else if (e.detail.name === "password") {
      this.password = e.detail.value;
    }
  }

  @State() isSigningUp: boolean = false;

  private name: string = "";
  private email: string = "";
  private password: string = "";

  async signupUser() {
    let signupPayload: signupPayloadInterface = generateSignupPayload(
      this.name,
      this.email,
      this.password
    );
    let { isValid, validationMessage } = validateSignupPayload(signupPayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isSigningUp = true;
    let { success, message } = await signupApi(signupPayload);
    this.isSigningUp = false;
    if (!success) {
      return alert(message);
    }
    this.authSuccessfulEventEmitter.emit();
  }

  componentWillLoad() {
    Store.activeView = "signup";
    document.title = "Signup | Sensefolks";
  }

  render() {
    return (
      <Host>
        <div class="form">
          <e-image
            src={Var.app.branding.logo.logotype.withoutBg}
            width="180px"
          ></e-image>
          <l-spacer value={1}></l-spacer>
          <p-dotgrid height="50px" width="100%"></p-dotgrid>
          <l-spacer value={2}></l-spacer>
          <e-text variant="display">Sign up</e-text>
          <l-spacer value={0.75}></l-spacer>
          <e-text>
            Have an account? <e-link url="/login">Log In</e-link>
          </e-text>
          <l-spacer value={2}></l-spacer>
          <e-button-oauth></e-button-oauth>
          <l-spacer value={2.5}></l-spacer>
          <l-separator variant="oauth"></l-separator>
          <l-spacer value={2.5}></l-spacer>
          <e-input type="text" name="name" placeholder="Name"></e-input>
          <l-spacer value={2}></l-spacer>
          <e-input type="email" name="email" placeholder="Email"></e-input>
          <l-spacer value={2}></l-spacer>
          <e-input
            type="password"
            name="password"
            placeholder="Password (Min. 8 letters)"
          ></e-input>
          <l-spacer value={2}></l-spacer>
          <e-button action="signupUser" active={this.isSigningUp} size="wide">
            Sign up
          </e-button>
          <l-spacer value={1}></l-spacer>
          <e-link url="/password-reset">Reset password</e-link>
          <l-spacer value={2}></l-spacer>
          <l-separator></l-separator>
          <l-spacer value={1.5}></l-spacer>
          <e-text variant="footnote">
            By logging in, you accept our{" "}
            <e-link variant="externalLink" url={Var.app.policy.tos.url}>
              terms of service
            </e-link>{" "}
            &{" "}
            <e-link variant="externalLink" url={Var.app.policy.privacy.url}>
              privacy policy
            </e-link>
          </e-text>
        </div>
      </Host>
    );
  }
}
