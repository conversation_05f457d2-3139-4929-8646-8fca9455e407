import { newSpecPage } from '@stencil/core/testing';
import { VPasswordReset } from '../v-password-reset';

describe('v-password-reset', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VPasswordReset],
      html: `<v-password-reset></v-password-reset>`,
    });
    expect(page.root).toEqualHtml(`
      <v-password-reset>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-password-reset>
    `);
  });
});
