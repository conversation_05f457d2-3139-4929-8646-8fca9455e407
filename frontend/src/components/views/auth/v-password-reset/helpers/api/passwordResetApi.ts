import { passwordResetPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const passwordResetApi = async (payload: passwordResetPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.password.details, {
      method: 'PUT',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log password reset error
    FrontendLogger.error('Error resetting password', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });
    return {
      success: false,
      message: 'Error resetting password',
      payload: null,
    };
  }
};
