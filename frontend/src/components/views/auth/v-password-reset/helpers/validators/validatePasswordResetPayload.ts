import Joi from 'joi';
import { passwordResetPayloadInterface } from '../../interfaces';

const validatePasswordResetSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .min(5)
    .max(128)
    .lowercase()
    .trim()
    .required(),
  newPassword: Joi.string().trim().min(8).required(),
  newPasswordRepeat: Joi.string().equal(Joi.ref('newPassword')).trim().required(),
  passwordResetCode: Joi.string().trim().length(6).required(),
});

export const validatePasswordResetPayload = (
  passwordResetPayload: passwordResetPayloadInterface,
) => {
  let { error } = validatePasswordResetSchema.validate(passwordResetPayload);
  if (error) {
    return { isValid: false, validationMessage: error.details[0].message };
  } else {
    return { isValid: true, validationMessage: '' };
  }
};
