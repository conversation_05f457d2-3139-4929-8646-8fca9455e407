import { Component, State, Listen, h } from '@stencil/core';
import { Store } from '../../../../global/script/store';
import { UpdateByAttributeApi } from '../../../../global/script/helpers';
import { Var } from '../../../../global/script/var';

@Component({
  tag: 'v-account',
  styleUrl: 'v-account.css',
  shadow: true,
})
export class VAccount {
  componentWillLoad() {
    Store.activeView = 'account';
    document.title = 'Account | Sensefolks';
  }

  @State() isEditingName: boolean = false;
  @State() isEditingEmail: boolean = false;
  @State() isEditingPassword: boolean = false;

  @State() isUpdating: boolean = false;
  @State() updateMessage: string = '';
  @State() updateSuccess: boolean = false;
  @State() currentField: string = '';

  @Listen('editableTextEvent') async handleEditableTextEvent(e) {
    let attribute: string = e.detail.payload.attribute;
    this.currentField = attribute;
    this.isUpdating = true;
    this.updateMessage = '';
    this.updateSuccess = false;

    if (attribute === 'name') {
      this.isEditingName = true;
    } else if (attribute === 'email') {
      this.isEditingEmail = true;
    } else if (attribute === 'password') {
      this.isEditingPassword = true;
    }

    try {
      let { success, message } = await UpdateByAttributeApi(
        Var.api.endpoint.account.details,
        e.detail.payload,
      );

      this.updateMessage = message;
      this.updateSuccess = success;

      if (success) {
        if (attribute === 'name') {
          Store.accountName = e.detail.payload.value;
          this.isEditingName = false;
        } else if (attribute === 'email') {
          Store.accountEmail = e.detail.payload.value;
          Store.isEmailVerified = false;
          this.isEditingEmail = false;
        } else if (attribute === 'password') {
          this.isEditingPassword = false;
        }
      }
    } catch (error) {
      this.updateMessage = `An error occurred while updating ${this.currentField}`;
      this.updateSuccess = false;
      console.error(error);
    } finally {
      this.isUpdating = false;
    }
  }

  render() {
    return (
      <c-main>
        <div class="container">
          <l-row justifyContent="flex-start">
            <e-image src="../../../assets/icon/dark/user-dark.svg" width="2em"></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Account Details</e-text>
          </l-row>
          <l-spacer value={2}></l-spacer>
          <e-text-editable
            label="name"
            type="text"
            value={Store.accountName}
            entity="account"
            attribute="name"
            active={this.isEditingName}
          ></e-text-editable>
          {this.updateMessage && !this.isUpdating && this.currentField === 'name' && (
            <p-notification
              message={this.updateMessage}
              theme={this.updateSuccess ? 'success' : 'danger'}
            ></p-notification>
          )}
          <l-spacer value={2}></l-spacer>
          <e-text-editable
            label="email"
            type="link"
            value={`${Store.accountEmail}`}
            entity="account"
            attribute="email"
            active={this.isEditingEmail}
          ></e-text-editable>

          {this.updateMessage && !this.isUpdating && this.currentField === 'email' && (
            <p-notification
              message={this.updateMessage}
              theme={this.updateSuccess ? 'success' : 'danger'}
            ></p-notification>
          )}

          <l-spacer value={2}></l-spacer>
          <e-text-editable
            label="password"
            type="password"
            value="********"
            entity="account"
            attribute="password"
            active={this.isEditingPassword}
          ></e-text-editable>

          {this.updateMessage && !this.isUpdating && this.currentField === 'password' && (
            <p-notification
              message={this.updateMessage}
              theme={this.updateSuccess ? 'success' : 'danger'}
            ></p-notification>
          )}

          <l-spacer value={2}></l-spacer>
          <e-link url="/account/delete" theme="danger">
            <l-row justifyContent="flex-start">
              <e-image src="../../../assets/icon/red/trash-red.svg" width="1em"></e-image>
              <l-spacer variant="horizontal" value={0.25}></l-spacer>
              <e-text>Delete account and data</e-text>
            </l-row>
          </e-link>
        </div>
      </c-main>
    );
  }
}
