import {
  Component,
  Event,
  EventEmitter,
  State,
  Listen,
  h,
} from "@stencil/core";
import { deleteAccountApi } from "./helpers";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-delete-account",
  styleUrl: "v-delete-account.css",
  shadow: true,
})
export class VDeleteAccount {
  @Event({
    eventName: "logoutEvent",
    bubbles: true,
  })
  logoutEventEmitter: EventEmitter;

  @Listen("buttonClickEvent") async handleButtonClickEvent(e) {
    if (e.detail.action === "deleteAccount") {
      this.deleteAccount();
    }
  }

  @State() isDeletingAccount: boolean = false;

  async deleteAccount() {
    this.isDeletingAccount = true;
    let { success, message } = await deleteAccountApi();
    this.isDeletingAccount = false;
    alert(message);
    if (!success) {
      return;
    }
    this.logoutEventEmitter.emit();
  }

  componentWillLoad() {
    Store.activeView = "deleteAccount";
    document.title = "Delete Account | Sensefolks";
  }

  render() {
    return (
      <c-main>
        <div class="container">
          <l-row justifyContent="flex-start">
            <e-image
              src="../../../assets/icon/dark/trash-dark.svg"
              width="2em"
            ></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Delete Account?</e-text>
          </l-row>{" "}
          <l-spacer value={2}></l-spacer>
          <e-text>
            Deleting your account is an irreversible step. Your account data and
            survey data will be <u>lost forever</u>.{" "}
          </e-text>
          <l-spacer value={1}></l-spacer>
          <e-text>
            <strong>
              Are you absolutely sure that you want to delete your account?
            </strong>
          </e-text>
          <l-spacer value={3}></l-spacer>
          <l-row justifyContent="space-between" align="center">
            <e-link url="/account">Back</e-link>
            <e-button
              action="deleteAccount"
              theme="danger"
              active={this.isDeletingAccount}
            >
              Yes, delete my account
            </e-button>
          </l-row>
        </div>
      </c-main>
    );
  }
}
