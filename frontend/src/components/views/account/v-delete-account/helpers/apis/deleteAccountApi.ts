import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const deleteAccountApi = async () => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.account.details, {
      method: 'DELETE',
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error deleting account:', error);
    return {
      success: false,
      message: 'Error deleting account',
      payload: null,
    };
  }
};
