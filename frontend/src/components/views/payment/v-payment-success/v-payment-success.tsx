import {
  Component,
  Prop,
  State,
  Host,
  h,
  FunctionalComponent,
} from "@stencil/core";
import { Store } from "../../../../global/script/store";

@Component({
  tag: "v-payment-success",
  styleUrl: "v-payment-success.css",
  shadow: true,
})
export class VPaymentSuccess {
  @Prop() sessionId: string;

  @State() isViewDataFetched: boolean = false;

  componentWillLoad() {
    Store.activeView = "paymentSuccess";
    document.title = "Payment Success | Sensefolks";
  }

  Skel: FunctionalComponent = () => (
    <c-card>
      <e-text>
        <strong>Confirming payment..</strong>
      </e-text>
      <div class="skel__line"></div>
      <div class="skel__line"></div>
      <div class="skel__line"></div>
    </c-card>
  );

  Default: FunctionalComponent = () => (
    <c-card>
      <e-text variant="display" theme="success">
        Payment Successful
      </e-text>
      <l-spacer value={1}></l-spacer>
      <e-text>
        You have upgraded to <strong>XYZ Plan</strong>
      </e-text>
      <l-spacer value={1}></l-spacer>
      <e-link url="/">Go to account</e-link>
    </c-card>
  );

  render() {
    return (
      <Host>
        {this.isViewDataFetched ? (
          <this.Default></this.Default>
        ) : (
          <this.Skel></this.Skel>
        )}
      </Host>
    );
  }
}
