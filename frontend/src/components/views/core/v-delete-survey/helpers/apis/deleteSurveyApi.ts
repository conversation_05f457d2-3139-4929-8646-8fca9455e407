import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const deleteSurveyApi = async (surveyId: string) => {
  try {
    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}`, {
      method: 'DELETE',
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log survey deletion error
    FrontendLogger.error('Error deleting survey', {
      surveyId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });
    return {
      success: false,
      message: 'Error deleting survey',
      payload: null,
    };
  }
};
