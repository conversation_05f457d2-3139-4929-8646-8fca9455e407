import { newSpecPage } from '@stencil/core/testing';
import { VEditSurvey } from '../v-edit-survey';

describe('v-edit-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VEditSurvey],
      html: `<v-edit-survey></v-edit-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-edit-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-edit-survey>
    `);
  });
});
