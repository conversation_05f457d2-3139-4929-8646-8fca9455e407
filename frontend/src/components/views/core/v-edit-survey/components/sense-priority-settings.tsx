import { Component, Prop, State, h, Listen } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
} from '../../../../../global/script/helpers';
import { Var, FrontendLogger } from '../../../../../global/script/var';

interface PriorityItem {
  title: string;
  description?: string;
  value: string;
}

@Component({
  tag: 'sense-priority-settings',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class SensePrioritySettings {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() updatingField: string = '';

  // Question field status
  @State() questionMessage: string = '';
  @State() questionSuccess: boolean = false;

  // Items field status
  @State() itemsMessage: string = '';
  @State() itemsSuccess: boolean = false;

  // Thank You Message field status
  @State() thankYouMessageMessage: string = '';
  @State() thankYouMessageSuccess: boolean = false;

  @State() question: string = '';
  @State() thankYouMessage: string = '';
  @State() isEditingQuestion: boolean = false;
  @State() isEditingThankYou: boolean = false;

  // Modal states for priority items
  @State() isPriorityItemModalOpen: boolean = false;
  @State() isEditingPriorityItem: boolean = false;
  @State() editingPriorityItem: PriorityItem | null = null;
  @State() editingPriorityItemIndex: number = -1;

  componentWillLoad() {
    if (this.survey && this.survey.config) {
      this.question = this.survey.config.question || '';
      this.thankYouMessage = this.survey.config.thankYouMessage || 'Thank you for your response!';
    }
  }

  @Listen('buttonClickEvent')
  async handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addPriorityItem') {
      this.isEditingPriorityItem = false;
      this.editingPriorityItem = null;
      this.editingPriorityItemIndex = -1;
      this.isPriorityItemModalOpen = true;
    }
  }

  @Listen('addCustomPriorityItem')
  async handleAddCustomPriorityItem(event: CustomEvent) {
    const newItem = event.detail.priorityItem;
    const isEditMode = this.isEditingPriorityItem;

    const currentItems = this.survey.config?.items || [];

    if (isEditMode && this.editingPriorityItemIndex >= 0) {
      // Update existing item
      currentItems[this.editingPriorityItemIndex] = newItem;
    } else {
      // Check if this item already exists (for new items)
      const isDuplicate = currentItems.some((item: PriorityItem) => item.value === newItem.value);

      if (isDuplicate) {
        this.itemsMessage = 'This priority item already exists';
        this.itemsSuccess = false;
        return;
      }

      currentItems.push(newItem);
    }

    const updatedConfig = { ...this.survey.config, items: currentItems };
    await this.updateConfig(updatedConfig, { attribute: 'items', value: currentItems });

    // Close the modal and reset edit state
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;
  }

  @Listen('priorityItemDeleteEvent')
  async handlePriorityItemDeleteEvent(event: CustomEvent) {
    await this.removeItem(event.detail.value);
  }

  @Listen('priorityItemEditEvent')
  handlePriorityItemEditEvent(event: CustomEvent) {
    this.editingPriorityItem = event.detail.item;
    this.editingPriorityItemIndex = event.detail.index;
    this.isEditingPriorityItem = true;
    this.isPriorityItemModalOpen = true;
  }

  @Listen('modalCloseEvent')
  handleModalCloseEvent() {
    this.isPriorityItemModalOpen = false;
    this.isEditingPriorityItem = false;
    this.editingPriorityItem = null;
    this.editingPriorityItemIndex = -1;
  }

  @Listen('editableTextEvent')
  async handleEditableTextEvent(event: CustomEvent) {
    const { attribute, value } = event.detail;

    if (attribute === 'question') {
      await this.updateQuestion(value);
    } else if (attribute === 'thankYouMessage') {
      await this.updateThankYouMessage(value);
    }
  }

  // Update the question
  private async updateQuestion(newQuestion: string) {
    this.updatingField = 'question';

    if (!newQuestion || newQuestion.trim() === '') {
      this.questionMessage = 'Question cannot be empty';
      this.questionSuccess = false;
      this.updatingField = '';
      return;
    }

    const updatedConfig = { ...this.survey.config, question: newQuestion };
    await this.updateConfig(updatedConfig, { attribute: 'question', value: newQuestion });
  }

  // Remove a priority item
  private async removeItem(itemValue: string) {
    const currentItems = this.survey.config?.items || [];
    const updatedItems = currentItems.filter((item: PriorityItem) => item.value !== itemValue);

    const updatedConfig = { ...this.survey.config, items: updatedItems };
    await this.updateConfig(updatedConfig, { attribute: 'items', value: updatedItems });
  }

  // Update thank you message
  private async updateThankYouMessage(newMessage: string) {
    this.updatingField = 'thankYouMessage';
    const updatedConfig = { ...this.survey.config, thankYouMessage: newMessage };
    await this.updateConfig(updatedConfig, { attribute: 'thankYouMessage', value: newMessage });
  }

  // Helper method to update config
  private async updateConfig(updatedConfig: any, obj: any) {
    // Set field-specific editing states
    if (obj.attribute === 'question') {
      this.isEditingQuestion = true;
    } else if (obj.attribute === 'thankYouMessage') {
      this.isEditingThankYou = true;
    }

    const updatePayload = GenerateUpdateByAttributePayload('config', updatedConfig);

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      if (success) {
        // Update local survey object
        this.survey = { ...this.survey, config: updatedConfig };

        // Set success messages based on attribute
        if (obj.attribute === 'question') {
          this.questionMessage = 'Question updated successfully';
          this.questionSuccess = true;
          this.question = obj.value;
        } else if (obj.attribute === 'items') {
          this.itemsMessage = 'Priority items updated successfully';
          this.itemsSuccess = true;
        } else if (obj.attribute === 'thankYouMessage') {
          this.thankYouMessageMessage = 'Thank you message updated successfully';
          this.thankYouMessageSuccess = true;
          this.thankYouMessage = obj.value;
        }
      } else {
        // Set error messages based on attribute
        if (obj.attribute === 'question') {
          this.questionMessage = message || 'Failed to update question';
          this.questionSuccess = false;
        } else if (obj.attribute === 'items') {
          this.itemsMessage = message || 'Failed to update priority items';
          this.itemsSuccess = false;
        } else if (obj.attribute === 'thankYouMessage') {
          this.thankYouMessageMessage = message || 'Failed to update thank you message';
          this.thankYouMessageSuccess = false;
        }
      }
    } catch (error) {
      FrontendLogger.error('Error updating survey config:', error);

      // Set generic error messages
      if (obj.attribute === 'question') {
        this.questionMessage = 'An error occurred while updating the question';
        this.questionSuccess = false;
      } else if (obj.attribute === 'items') {
        this.itemsMessage = 'An error occurred while updating priority items';
        this.itemsSuccess = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = 'An error occurred while updating thank you message';
        this.thankYouMessageSuccess = false;
      }
    } finally {
      // Reset editing states and updating field
      this.isEditingQuestion = false;
      this.isEditingThankYou = false;
      this.updatingField = '';

      // Clear messages after 4 seconds
      setTimeout(() => {
        this.questionMessage = '';
        this.itemsMessage = '';
        this.thankYouMessageMessage = '';
      }, 4000);
    }
  }

  render() {
    return (
      <div class="settings-section">
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>1. What priority question would you like to ask?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Question"
          type="text"
          value={this.question}
          entity="survey"
          attribute="question"
          bypass={true}
          active={this.isEditingQuestion}
        ></e-text-editable>

        {this.questionMessage && this.updatingField !== 'question' && (
          <p-notification
            message={this.questionMessage}
            theme={this.questionSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>2. What priority items would you like users to choose from?</strong>
        </e-text>
        <e-text variant="footnote">
          Add items that users can prioritize. Each item can have a title and optional description.
        </e-text>
        <l-spacer value={1}></l-spacer>

        <e-button variant="primary" action="addPriorityItem">
          + Add Item
        </e-button>

        <l-spacer value={1}></l-spacer>

        {this.survey.config?.items && this.survey.config.items.length > 0 && (
          <div class="priority-items-list">
            {this.survey.config.items.map((item: PriorityItem, index: number) => (
              <p-priority-item item={JSON.stringify(item)} index={index}></p-priority-item>
            ))}
          </div>
        )}

        {(!this.survey.config?.items || this.survey.config.items.length === 0) && (
          <div class="empty-state">
            <e-text variant="footnote">No priority items added yet</e-text>
          </div>
        )}

        {this.itemsMessage && this.updatingField !== 'items' && (
          <p-notification
            message={this.itemsMessage}
            theme={this.itemsSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>4. Thank You Message</strong>
        </e-text>
        <e-text variant="footnote">
          This message will be shown to respondents after they submit their response
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Thank You Message"
          type="text"
          value={this.thankYouMessage}
          entity="survey"
          attribute="thankYouMessage"
          bypass={true}
          active={this.isEditingThankYou}
        ></e-text-editable>
        {this.thankYouMessageMessage && this.updatingField !== 'thankYouMessage' && (
          <p-notification
            message={this.thankYouMessageMessage}
            theme={this.thankYouMessageSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        {/* Modal for priority item */}
        <p-modal
          is-open={this.isPriorityItemModalOpen}
          modal-title={this.isEditingPriorityItem ? 'Edit Item' : '+ Add Item'}
        >
          {this.isPriorityItemModalOpen && (
            <p-priority-item-form
              editing-item={
                this.editingPriorityItem ? JSON.stringify(this.editingPriorityItem) : ''
              }
              is-edit-mode={this.isEditingPriorityItem}
            ></p-priority-item-form>
          )}
        </p-modal>
      </div>
    );
  }
}
