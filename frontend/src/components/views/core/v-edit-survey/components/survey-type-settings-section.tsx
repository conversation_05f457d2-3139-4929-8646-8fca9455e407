import { Component, Prop, h } from '@stencil/core';

@Component({
  tag: 'survey-type-settings-section',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class SurveyTypeSettingsSection {
  @Prop() surveyId: string;
  @Prop() survey: any;

  render() {
    switch (this.survey?.type) {
      case 'sensePoll':
        return (
          <sense-poll-settings surveyId={this.surveyId} survey={this.survey}></sense-poll-settings>
        );

      case 'senseQuery':
        return (
          <sense-query-settings
            surveyId={this.surveyId}
            survey={this.survey}
          ></sense-query-settings>
        );

      case 'sensePriority':
        return (
          <sense-priority-settings
            surveyId={this.surveyId}
            survey={this.survey}
          ></sense-priority-settings>
        );
    }
  }
}
