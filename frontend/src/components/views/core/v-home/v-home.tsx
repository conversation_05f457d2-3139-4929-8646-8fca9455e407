import {
  Component,
  FunctionalComponent,
  Event,
  EventEmitter,
  Listen,
  State,
  h,
} from '@stencil/core';
import { Store } from '../../../../global/script/store';
import { getAllSurveysApi } from './helpers';
import { Router } from '../../../../';

@Component({
  tag: 'v-home',
  styleUrl: 'v-home.css',
  shadow: true,
})
export class VHome {
  @Event({
    eventName: 'routeToEvent',
    bubbles: true,
  })
  routeToEvent: EventEmitter;

  @State() compState: string = '';
  private surveys: any = [];

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    if (e.detail.action === 'createSurvey') {
      Router.push('/surveys/create');
    }
  }

  @Listen('wizardCompletionEvent') async handleWizardCompletionEvent() {
    this.getAllSurveys();
  }

  componentWillLoad() {
    Store.activeView = 'surveys';
    document.title = 'Surveys | Sensefolks';
    this.getAllSurveys();
  }

  async getAllSurveys() {
    if (this.compState != 'fetching') {
      this.compState = 'fetching';
    }
    let { success, message, payload } = await getAllSurveysApi();
    if (!success) {
      this.compState = 'error';
      return alert(message);
    }
    this.surveys = payload;
    if (this.surveys.length > 0) {
      this.surveys = [...this.surveys];
      this.compState = 'ready';
    } else {
      this.compState = 'empty';
    }
  }

  Fetching: FunctionalComponent = () => (
    <div class="centered">
      <e-spinner theme="dark"></e-spinner>
    </div>
  );

  Empty: FunctionalComponent = () => (
    <c-card>
      {/* <e-text variant="display">Get started in 2 mins</e-text> */}
      <l-row justifyContent="flex-start">
        {/* <e-image src="../../../assets/icon/dark/rocket-launch-dark.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer> */}
        <e-text variant="display">Get started in 2 mins</e-text>
      </l-row>
      <l-spacer value={1}></l-spacer>
      {/* <l-separator></l-separator> */}
      <p-dotgrid width="100%" height="50px"></p-dotgrid>
      <l-spacer value={1}></l-spacer>
      <e-list>
        <e-list-item>
          <e-text variant="footnote">STEP 1</e-text>
          <e-text>Customize your survey</e-text>
        </e-list-item>
        <l-spacer value={2}></l-spacer>
        <e-list-item>
          <e-text variant="footnote">STEP 2</e-text>
          <e-text>Embed survey on your website or share as link</e-text>
        </e-list-item>
        <l-spacer value={2}></l-spacer>
        <e-list-item>
          <e-text variant="footnote">STEP 3</e-text>
          <e-text>Get customer insights. Make solid decisions ✨</e-text>
        </e-list-item>
        <l-spacer value={3}></l-spacer>
      </e-list>
      <l-spacer value={2.5}></l-spacer>
      <l-row justifyContent="center">
        <e-button variant="primary" action="createSurvey">
          Create Survey
        </e-button>
      </l-row>
    </c-card>
  );

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return (
        <e-text variant="footnote">
          <e-pill color="purple">SensePrice</e-pill>
        </e-text>
      );
    } else if (value === 'senseChoice') {
      return (
        <e-text variant="footnote">
          <e-pill color="blue">SenseChoice</e-pill>
        </e-text>
      );
    } else if (value === 'sensePoll') {
      return (
        <e-text variant="footnote">
          <e-pill color="indigo">SensePoll</e-pill>
        </e-text>
      );
    } else if (value === 'senseQuery') {
      return (
        <e-text variant="footnote">
          <e-pill color="turquoise">SenseQuery</e-pill>
        </e-text>
      );
    }
  }

  handleSurveyClick(value: string) {
    this.routeToEvent.emit({
      route: `/surveys/${value}/results`,
    });
  }

  Ready: FunctionalComponent = () => (
    <div id="ready-container">
      <l-row justifyContent="flex-start">
        <e-image src="../../../assets/icon/dark/note-dark.svg" width="2em"></e-image>
        <l-spacer variant="horizontal" value={0.5}></l-spacer>
        <e-text variant="display">My Surveys</e-text>
      </l-row>{' '}
      <l-spacer value={2}></l-spacer>
      <ul class="survey-list">
        {this.surveys.map(survey => (
          <li onClick={() => this.handleSurveyClick(survey.id)}>
            <l-row align="flex-start">
              {this.generateSurveyPill(survey.type)}{' '}
              {/* <e-text variant="footnote">{survey.responseCount} responses</e-text> */}
              <e-text variant="footnote">{survey.timestamp}</e-text>
            </l-row>
            <l-spacer value={0.5}></l-spacer>
            <e-text>{survey.title}</e-text>
          </li>
        ))}
      </ul>
      <div id="fab">
        <e-button variant="primary" action="createSurvey">
          Create Survey
        </e-button>
      </div>
    </div>
  );

  Error: FunctionalComponent = () => (
    <div id="error-container">
      <article>
        <e-text variant="display">Could not fetch your surveys :(</e-text>
        <l-spacer value={1}></l-spacer>
        <e-text>
          If the issue persists, kindly visit the <e-link url="/support">Support page</e-link> and
          report the issue
        </e-text>
      </article>
    </div>
  );

  render() {
    return (
      <c-main>
        {this.compState === 'fetching' && <this.Fetching></this.Fetching>}
        {this.compState === 'empty' && <this.Empty></this.Empty>}
        {this.compState === 'ready' && <this.Ready></this.Ready>}
        {this.compState === 'error' && <this.Error></this.Error>}
      </c-main>
    );
  }
}
