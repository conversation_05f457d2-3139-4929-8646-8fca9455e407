import { Component, Prop, State, Listen, h } from '@stencil/core';
import { getSurveyEmbedCodeApi } from './helpers';
import { GetSurveyApi } from '../../../../global/script/helpers';
import { Store } from '../../../../global/script/store';
import { FrontendLogger } from '../../../../global/script/var';

@Component({
  tag: 'v-embed-survey',
  styleUrl: 'v-embed-survey.css',
  shadow: true,
})
export class VEmbedSurvey {
  @Prop() surveyId: string;
  @State() isLoading: boolean = true;
  @State() loadError: boolean = false;
  @State() embedCode: string = '';
  @State() publicKey: string = '';
  @State() surveyType: string = '';
  @State() surveyTitle: string = '';
  @State() isCopied: boolean = false;
  @State() copyButtonText: string = 'Copy Code';

  @Listen('buttonClickEvent') async handleButtonClickEvent(e: CustomEvent) {
    if (e.detail.action === 'copyEmbedCode') {
      this.copyToClipboard();
    }
  }

  /**
   * Copies the embed code to the user's clipboard
   * Provides user feedback through button state changes
   */
  async copyToClipboard() {
    try {
      // DEBUG: Log clipboard copy attempt
      FrontendLogger.debug('Copying embed code to clipboard', {
        hasEmbedCode: !!this.embedCode,
        embedCodeLength: this.embedCode?.length || 0,
      });

      await navigator.clipboard.writeText(this.embedCode);
      this.isCopied = true;
      this.copyButtonText = 'Copied!';

      FrontendLogger.debug('Embed code copied to clipboard successfully');

      // Reset the button text after 2 seconds
      setTimeout(() => {
        this.copyButtonText = 'Copy Code';
        this.isCopied = false;
      }, 2000);
    } catch (err) {
      // DEBUG: Log clipboard copy error
      FrontendLogger.error('Failed to copy embed code to clipboard', {
        error: err instanceof Error ? err.message : String(err),
        hasEmbedCode: !!this.embedCode,
      });
      alert('Failed to copy embed code. Please try again.');
    }
  }

  async getSurveyDetails() {
    this.isLoading = true;
    let { success, message, payload } = await GetSurveyApi(this.surveyId);

    if (!success) {
      this.loadError = true;
      this.isLoading = false;
      return alert(message);
    }

    if (payload && payload.title) {
      this.surveyTitle = payload.title;
    }

    await this.getEmbedCode();
  }

  async getEmbedCode() {
    let { success, message, payload } = await getSurveyEmbedCodeApi(this.surveyId);
    this.isLoading = false;

    if (!success) {
      this.loadError = true;
      return alert(message);
    }

    if (payload) {
      this.embedCode = payload.embedCode;
      this.publicKey = payload.publicKey;
      this.surveyType = payload.type;
    }
  }

  componentWillLoad() {
    Store.activeView = 'embedSurvey';
    document.title = 'Embed Survey | Sensefolks';
    this.getSurveyDetails();
  }

  render() {
    if (this.isLoading) {
      return (
        <c-main>
          <div class="container container__spinner">
            <e-spinner theme="dark"></e-spinner>
          </div>
        </c-main>
      );
    }

    if (this.loadError) {
      return (
        <c-main>
          <div class="container">
            <e-text variant="display">Error</e-text>
            <l-spacer value={1}></l-spacer>
            <e-text>Could not load survey embed details. Please try again later.</e-text>
            <l-spacer value={2}></l-spacer>
            <e-link url={`/surveys/${this.surveyId}/results`}>← Back</e-link>
          </div>
        </c-main>
      );
    }

    return (
      <c-main>
        <div class="container">
          <l-row>
            <e-link url={`/surveys/${this.surveyId}/results`}>← Back</e-link>
            <div></div>
          </l-row>
          <l-spacer value={2}></l-spacer>
          <l-row justifyContent="flex-start">
            <e-image src="../../../assets/icon/dark/code-dark.svg" width="2em"></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Embed Survey</e-text>
          </l-row>
          <l-spacer value={1}></l-spacer>
          {this.surveyTitle && (
            <div>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
              <e-text variant="footnote">SURVEY TITLE</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="heading">{this.surveyTitle}</e-text>
              <l-spacer value={1}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
            </div>
          )}
          <l-spacer value={2}></l-spacer>
          <e-text>
            <strong>Add this code to your website or app:</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <div class="embed-code-container">
            <div class="embed-code">
              <e-text class="code-text">{this.embedCode}</e-text>
            </div>
            <l-spacer value={0.25}></l-spacer>
            <l-row direction="row-reverse">
              <e-button
                action="copyEmbedCode"
                theme={this.isCopied ? 'success' : 'default'}
                size="small"
              >
                <span class={this.isCopied ? 'copied-text' : ''}>{this.copyButtonText}</span>
              </e-button>
            </l-row>
          </div>
          <l-spacer value={0.5}></l-spacer>
          <e-text variant="footnote">
            This code will embed the {this.surveyType} survey directly into your website / app
          </e-text>
          <l-spacer value={3}></l-spacer>
        </div>
      </c-main>
    );
  }
}
