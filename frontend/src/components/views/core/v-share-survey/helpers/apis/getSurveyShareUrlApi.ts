import { Var } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const getSurveyShareUrlApi = async (surveyId: string) => {
  try {
    const result = await ApiWrapper(`${Var.api.endpoint.surveys}/${surveyId}/share`, {
      method: 'GET',
      includeCsrf: true, // Include CSRF token for authenticated requests
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    console.error('Error fetching survey share URL:', error);
    return {
      success: false,
      message: 'Error fetching survey share URL',
      payload: null,
    };
  }
};
