import { Component, Prop, State, Listen, h } from '@stencil/core';
import { getSurveyShareUrlApi } from './helpers';
import { GetSurveyApi } from '../../../../global/script/helpers';
import { Store } from '../../../../global/script/store';
import { FrontendLogger } from '../../../../global/script/var';

@Component({
  tag: 'v-share-survey',
  styleUrl: 'v-share-survey.css',
  shadow: true,
})
export class VShareSurvey {
  @Prop() surveyId: string;
  @State() isLoading: boolean = true;
  @State() loadError: boolean = false;
  @State() shareUrl: string = '';
  @State() surveyTitle: string = '';
  @State() isCopied: boolean = false;
  @State() copyButtonText: string = 'Copy Link';

  @Listen('buttonClickEvent') async handleButtonClickEvent(e: CustomEvent) {
    if (e.detail.action === 'copyShareUrl') {
      this.copyToClipboard();
    }
  }

  /**
   * Copies the share URL to the user's clipboard
   * Provides user feedback through button state changes
   */
  async copyToClipboard() {
    try {
      // DEBUG: Log clipboard copy attempt
      FrontendLogger.debug('Copying share URL to clipboard', {
        hasShareUrl: !!this.shareUrl,
        shareUrlLength: this.shareUrl?.length || 0,
      });

      await navigator.clipboard.writeText(this.shareUrl);
      this.isCopied = true;
      this.copyButtonText = 'Copied!';

      FrontendLogger.debug('Share URL copied to clipboard successfully');

      // Reset the button text after 2 seconds
      setTimeout(() => {
        this.copyButtonText = 'Copy Link';
        this.isCopied = false;
      }, 2000);
    } catch (err) {
      // DEBUG: Log clipboard copy error
      FrontendLogger.error('Failed to copy share URL to clipboard', {
        error: err instanceof Error ? err.message : String(err),
        hasShareUrl: !!this.shareUrl,
      });
      alert('Failed to copy link. Please try again.');
    }
  }

  async getSurveyDetails() {
    this.isLoading = true;
    let { success, message, payload } = await GetSurveyApi(this.surveyId);

    if (!success) {
      this.loadError = true;
      this.isLoading = false;
      return alert(message);
    }

    if (payload && payload.title) {
      this.surveyTitle = payload.title;
    }

    await this.getShareUrl();
  }

  async getShareUrl() {
    let { success, message, payload } = await getSurveyShareUrlApi(this.surveyId);
    this.isLoading = false;

    if (!success) {
      this.loadError = true;
      return alert(message);
    }

    if (payload && payload.shareUrl) {
      this.shareUrl = payload.shareUrl;
    }
  }

  componentWillLoad() {
    Store.activeView = 'shareSurvey';
    document.title = 'Share Survey | Sensefolks';
    this.getSurveyDetails();
  }

  render() {
    if (this.isLoading) {
      return (
        <c-main>
          <div class="container container__spinner">
            <e-spinner theme="dark"></e-spinner>
          </div>
        </c-main>
      );
    }

    if (this.loadError) {
      return (
        <c-main>
          <div class="container">
            <e-text variant="display">Error</e-text>
            <l-spacer value={1}></l-spacer>
            <e-text>Could not load survey sharing details. Please try again later.</e-text>
            <l-spacer value={2}></l-spacer>
            <e-link url={`/surveys/${this.surveyId}/results`}>Back to Survey</e-link>
          </div>
        </c-main>
      );
    }

    return (
      <c-main>
        <div class="container">
          <l-row>
            <e-link url={`/surveys/${this.surveyId}/results`}>← Back</e-link>
            <div></div>
          </l-row>
          <l-spacer value={2}></l-spacer>
          <l-row justifyContent="flex-start">
            <e-image src="../../../assets/icon/dark/share-dark.svg" width="2em"></e-image>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="display">Share Survey</e-text>
          </l-row>
          <l-spacer value={1}></l-spacer>
          {this.surveyTitle && (
            <div>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
              <e-text variant="footnote">SURVEY TITLE</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text variant="heading">{this.surveyTitle}</e-text>
              <l-spacer value={1}></l-spacer>
              <l-separator></l-separator>
              <l-spacer value={1}></l-spacer>
            </div>
          )}
          <l-spacer value={2}></l-spacer>
          <e-text>
            <strong>Share this link with your audience:</strong>
          </e-text>
          <l-spacer value={0.5}></l-spacer>
          <div class="share-url-container">
            <e-text variant="footnote">SURVEY LINK</e-text>
            <e-text>{this.shareUrl}</e-text>
            <l-row direction="row-reverse">
              <e-button
                action="copyShareUrl"
                theme={this.isCopied ? 'success' : 'default'}
                size="small"
              >
                {this.copyButtonText}
              </e-button>
            </l-row>
          </div>
          <l-spacer value={0.5}></l-spacer>
          <e-text variant="footnote">
            Anyone with this link can access and respond to your survey.
          </e-text>
          <l-spacer value={3}></l-spacer>
        </div>
      </c-main>
    );
  }
}
