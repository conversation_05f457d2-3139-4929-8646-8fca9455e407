import { senseQueryConfigInterface } from "../../interfaces";

export const generateSenseQueryConfig = (
  question: string,
  categories: { label: string; value: string }[],
  thankYouMessage: string
) => {
  let payload: senseQueryConfigInterface = {
    question: question,
    categories: categories || [],
    thankYouMessage: thankYouMessage || "Thank you for your response!",
  };

  return payload;
};
