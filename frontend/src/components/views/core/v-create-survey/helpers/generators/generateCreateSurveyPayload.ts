import { createSurveyPayloadInterface } from '../../interfaces';

export const generateCreateSurveyPayload = (
  type: string,
  title: string,
  distribution: string,
  embedUrl: string,
  tags: string[],
  config: object,
  respondentDetails: Array<{
    label: string;
    value: string;
    inputType?: string;
  }>,
) => {
  let payload: createSurveyPayloadInterface = {
    type: type.trim(),
    title: title.trim(),
    distribution: distribution.trim(),
    embedUrl: embedUrl.trim(),
    tags: tags,
    config: config,
    respondentDetails: respondentDetails,
  };

  return payload;
};
