import { createSurveyPayloadInterface } from '../../interfaces';
import { Var, FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export const createSurveyApi = async (payload: createSurveyPayloadInterface) => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.surveys, {
      method: 'POST',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    // DEBUG: Log survey creation error
    FrontendLogger.error('Error creating survey', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
    });
    return {
      success: false,
      message: 'Error creating survey',
      payload: null,
    };
  }
};
