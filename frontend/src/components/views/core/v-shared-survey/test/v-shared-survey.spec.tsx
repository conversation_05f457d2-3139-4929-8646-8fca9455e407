import { newSpecPage } from '@stencil/core/testing';
import { VSharedSurvey } from '../v-shared-survey';

describe('v-shared-survey', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VSharedSurvey],
      html: `<v-shared-survey></v-shared-survey>`,
    });
    expect(page.root).toEqualHtml(`
      <v-shared-survey>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-shared-survey>
    `);
  });
});
