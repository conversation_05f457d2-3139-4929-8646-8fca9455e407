import { newSpecPage } from '@stencil/core/testing';
import { VSurveyResults } from '../v-survey-results';

describe('v-survey-results', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [VSurveyResults],
      html: `<v-survey-results></v-survey-results>`,
    });
    expect(page.root).toEqualHtml(`
      <v-survey-results>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </v-survey-results>
    `);
  });
});
