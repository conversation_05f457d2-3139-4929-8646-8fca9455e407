import { Component, Prop, Event, EventEmitter, h } from '@stencil/core';
import { TabItem } from './types';

@Component({
  tag: 'p-tabnav',
  styleUrl: 'p-tabnav.css',
  shadow: true,
})
export class PTabnav {
  @Prop() tabs: TabItem[] = [];
  @Prop() activeTab: string = '';

  @Event() tabChange: EventEmitter<string>;

  handleTabClick(tabId: string) {
    this.tabChange.emit(tabId);
  }

  render() {
    return (
      <div class="tab-navigation">
        {this.tabs.map(tab => (
          <div
            class={`tab-item ${this.activeTab === tab.id ? 'tab-item--active' : ''}`}
            onClick={() => this.handleTabClick(tab.id)}
          >
            <e-text>{tab.label}</e-text>
          </div>
        ))}
      </div>
    );
  }
}
