import { newSpecPage } from '@stencil/core/testing';
import { PTabnav } from '../p-tabnav';

describe('p-tabnav', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PTabnav],
      html: `<p-tabnav></p-tabnav>`,
    });
    expect(page.root).toEqualHtml(`
      <p-tabnav>
        <mock:shadow-root>
          <div class="tab-navigation"></div>
        </mock:shadow-root>
      </p-tabnav>
    `);
  });
});
