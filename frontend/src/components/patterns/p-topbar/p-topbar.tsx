import { Component, State, Host, Listen, h } from "@stencil/core";
import { Var } from "../../../global/script/var";

@Component({
  tag: "p-topbar",
  styleUrl: "p-topbar.css",
  shadow: true,
})
export class PTopbar {
  @State() isMobileMenuOpen: boolean = false;

  @Listen("buttonClickEvent") async handleButtonClickEvent(e) {
    if (e.detail.action === "openMobileMenu") {
      this.isMobileMenuOpen = true;
    } else if (e.detail.action === "closeMobileMenu") {
      this.isMobileMenuOpen = false;
    }
  }

  render() {
    return (
      <Host>
        <l-row justifyContent="space-between" align="anchor-center">
          <e-link url="/">
            <e-image
              src={Var.app.branding.logo.logomark.withoutBg}
              width="24px"
            ></e-image>
          </e-link>
          <e-text>
            Trial Period . <e-link url="/billing">Upgrade Now</e-link>
          </e-text>
          <div class="hide-on-mobile">
            <p-user-control></p-user-control>
          </div>
          <div class="show-on-mobile">
            <e-button action="openMobileMenu" variant="light">
              <e-image
                src="../../../assets/icon/blue/hamburger-menu-blue.svg"
                width="1.5em"
              ></e-image>
            </e-button>
          </div>
        </l-row>
        {this.isMobileMenuOpen && (
          <div id="mobile-menu" class="show-on-mobile">
            <div id="mobile-menu__inner">
              <l-row justifyContent="space-between" align="center">
                <div></div>
                <e-button action="closeMobileMenu" variant="light">
                  <e-image
                    src="../../../assets/icon/red/x-red.svg"
                    width="1.25em"
                  ></e-image>
                </e-button>
              </l-row>
              <l-spacer value={2}></l-spacer>
              <e-list>
                <e-list-item>
                  <e-link url="/">
                    <l-row>
                      <e-image
                        src="../../../assets/icon/light/house.svg"
                        width="1em"
                      ></e-image>
                      <e-text>Home</e-text>
                    </l-row>
                  </e-link>
                </e-list-item>
                <l-spacer value={1}></l-spacer>
                <l-separator></l-separator>
                <l-spacer value={1}></l-spacer>
                <e-list-item>
                  <e-link url="/account">
                    <l-row>
                      <e-image
                        src="../../../assets/icon/light/user.svg"
                        width="1em"
                      ></e-image>
                      <e-text>Account</e-text>
                    </l-row>
                  </e-link>
                </e-list-item>
                <l-spacer value={0.5}></l-spacer>
                <e-list-item>
                  <e-link url="/billing">
                    <l-row>
                      <e-image
                        src="../../../assets/icon/light/money.svg"
                        width="1em"
                      ></e-image>
                      <e-text>Billing</e-text>
                    </l-row>
                  </e-link>
                </e-list-item>
                <l-spacer value={0.5}></l-spacer>
                <e-list-item>
                  <e-link variant="externalLink" url={Var.app.contact.url}>
                    Support
                  </e-link>
                </e-list-item>
                <l-spacer value={1}></l-spacer>
                <l-separator></l-separator>
                <l-spacer value={1}></l-spacer>
                <e-list-item>
                  <e-button variant="link" action="logout">
                    <e-text>Logout</e-text>
                  </e-button>
                </e-list-item>
              </e-list>
            </div>
          </div>
        )}
      </Host>
    );
  }
}
