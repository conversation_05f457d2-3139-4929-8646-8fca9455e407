import { newSpecPage } from '@stencil/core/testing';
import { PDotgrid } from '../p-dotgrid';

describe('p-dotgrid', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PDotgrid],
      html: `<p-dotgrid></p-dotgrid>`,
    });
    expect(page.root).toEqualHtml(`
      <p-dotgrid>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </p-dotgrid>
    `);
  });
});
