import { Component, Prop, Host, h } from "@stencil/core";
import { LooseObjectInterface } from "../../../global/script/interfaces";

@Component({
  tag: "p-dotgrid",
  styleUrl: "p-dotgrid.css",
  shadow: true,
})
export class PDotgrid {
  @Prop() height: string = "100px";
  @Prop() width: string = "100";

  private classMap: LooseObjectInterface = {};

  componentWillLoad() {
    this.generateClassMap();
  }

  generateClassMap() {
    this.classMap.height = this.height;
    this.classMap.width = this.width;
  }

  render() {
    return <Host style={this.classMap}></Host>;
  }
}
