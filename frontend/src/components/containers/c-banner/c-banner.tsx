import { Component, Prop, Host, h } from '@stencil/core';

@Component({
  tag: 'c-banner',
  styleUrl: 'c-banner.css',
  shadow: true,
})
export class CBanner {
  @Prop() theme: string = 'default';
  @Prop() position: string = 'inline';

  private classList: string = 'banner';

  componentWillLoad() {
    this.generateClassList();
  }

  generateClassList() {
    this.classList = `banner banner__theme--${this.theme} banner__position--${this.position}`;
  }

  render() {
    return (
      <Host class={this.classList}>
        <slot></slot>
      </Host>
    );
  }
}
