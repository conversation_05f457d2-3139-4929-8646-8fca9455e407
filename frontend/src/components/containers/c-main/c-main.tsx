import { Component, Prop, Host, h } from "@stencil/core";

@Component({
  tag: "c-main",
  styleUrl: "c-main.css",
  shadow: true,
})
export class CMain {
  @Prop() variant: string = "default";

  private classList: string = "";

  componentWillLoad() {
    this.generateClassList();
  }

  generateClassList() {
    this.classList = this.variant;
  }

  render() {
    return (
      <Host class={this.classList}>
        <slot></slot>
      </Host>
    );
  }
}
