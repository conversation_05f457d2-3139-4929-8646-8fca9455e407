import { newSpecPage } from '@stencil/core/testing';
import { EImage } from '../e-image';

describe('e-image', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [EImage],
      html: `<e-image></e-image>`,
    });
    expect(page.root).toEqualHtml(`
      <e-image>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-image>
    `);
  });
});
