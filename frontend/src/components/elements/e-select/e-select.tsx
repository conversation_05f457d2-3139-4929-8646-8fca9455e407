import { Component, Prop, Event, EventEmitter, State, Watch, h } from '@stencil/core';

@Component({
  tag: 'e-select',
  styleUrl: 'e-select.css',
  shadow: true,
})
export class ESelect {
  @Event({
    eventName: 'selectChangeEvent',
    bubbles: true,
  })
  selectChangeEventEmitter: EventEmitter;

  @Prop() options: any;
  @Prop() name: string;
  @Prop() resetTrigger: number = 0;

  @State() selectedValue: string = '';

  private parsedOptions: any;
  private selectElement!: HTMLSelectElement;

  componentWillLoad() {
    this.parseOptionsString();
    this.init();
  }

  @Watch('resetTrigger') resetTriggerWatcher(newVal: number, oldVal: number) {
    if (newVal !== oldVal && newVal > 0) {
      setTimeout(() => {
        this.resetSelect();
      }, 10);
    }
  }

  parseOptionsString() {
    this.parsedOptions = JSON.parse(this.options);
  }

  init() {
    this.selectedValue = this.parsedOptions[0].value.trim();
    this.selectChangeEventEmitter.emit({
      name: this.name,
      value: this.selectedValue,
    });
  }

  resetSelect() {
    if (this.parsedOptions && this.parsedOptions.length > 0) {
      const firstValue = this.parsedOptions[0].value.trim();
      this.selectedValue = firstValue;

      // Force DOM update
      if (this.selectElement) {
        this.selectElement.selectedIndex = 0;
        this.selectElement.value = firstValue;
      }

      // Emit the change event
      this.selectChangeEventEmitter.emit({
        name: this.name,
        value: this.selectedValue,
      });
    }
  }

  handleSelectChange(e) {
    this.selectedValue = e.target.value.trim();
    this.selectChangeEventEmitter.emit({
      name: this.name,
      value: this.selectedValue,
      label: e.target.options[e.target.selectedIndex].text,
    });
  }

  componentDidRender() {
    if (this.selectElement && this.selectedValue) {
      this.selectElement.value = this.selectedValue;
    }
  }

  render() {
    return (
      <select
        onChange={e => this.handleSelectChange(e)}
        ref={el => (this.selectElement = el as HTMLSelectElement)}
      >
        {this.parsedOptions.map(option => (
          <option value={option.value} selected={option.value === this.selectedValue}>
            {option.label}
          </option>
        ))}
      </select>
    );
  }
}
