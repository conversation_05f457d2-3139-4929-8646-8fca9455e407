a {
  text-decoration: none;
  color: var(--color__indigo--500);
  transition: all 0.25s;
}
a:hover {
  color: var(--color__indigo--700);
  cursor: pointer;
}
a:active {
  color: var(--color__indigo--400);
}

/* Nav link */
.link__nav--default {
  display: block;
  box-sizing: border-box;
  padding: 0.25em 1em;
}
.link__nav--default-active {
  display: block;
  box-sizing: border-box;
  color: var(--color__indigo--700);
  background: var(--color__indigo--50);
  padding: 0.25em 1em;
  border-radius: var(--border-radius);
}

/* Card link */
.link__card--default {
  width: 100%;
  border-radius: var(--border-radius);
  padding: calc(var(--padding) / 2) var(--padding);
  border: var(--border);
}
.link__card--default a:hover {
  background: var(--color__indigo--50);
}

/* Tab link */
.link__tab--default {
  margin-right: 3em;
}
.link__tab--default-active {
  margin-right: 3em;
  border-bottom: 2px solid var(--color__indigo--600);
}

/* Danger theme */
.link__default--danger {
  color: var(--color__red--700);
}
.link__default--danger:hover {
  color: var(--color__red--900);
}
.link__default--danger:active {
  color: var(--color__red--light);
}
