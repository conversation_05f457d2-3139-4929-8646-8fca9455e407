import {
  Component,
  Event,
  State,
  EventEmitter,
  Prop,
  Watch,
  h,
} from "@stencil/core";

@Component({
  tag: "e-input",
  styleUrl: "e-input.css",
  shadow: true,
})
export class EInput {
  @Event({
    eventName: "inputEvent",
    bubbles: true,
  })
  inputEvent: EventEmitter;

  @Prop() label: string;
  @Prop() type: string;
  @Prop() name: string;
  @Prop() placeholder: string = "Your text";
  @Prop() value: string;
  @Prop() checked: boolean = false;

  @State() isChecked: boolean = false;

  componentWillLoad() {
    if (this.type === "radio" || this.type === "checkbox") {
      this.isChecked = this.checked;
    }
  }

  @Watch("checked") checkedPropWatcher(newVal: boolean, oldVal: boolean) {
    if (newVal != oldVal) {
      this.isChecked = newVal;
    }
  }

  handleInputEvent(e) {
    if (
      this.type === "email" ||
      this.type === "number" ||
      this.type === "password" ||
      this.type === "text"
    ) {
      this.inputEvent.emit({
        name: this.name,
        value: e.target.value.trim(),
      });
    } else if (this.type === "radio" || this.type === "checkbox") {
      this.inputEvent.emit({
        name: this.name,
        value: e.target.value.trim(),
        label: this.label ? this.label.trim() : "",
        isChecked: e.target.checked,
      });
    }
  }

  render() {
    if (
      this.type === "email" ||
      this.type === "number" ||
      this.type === "password" ||
      this.type === "text"
    ) {
      return (
        <input
          type={this.type}
          placeholder={this.placeholder}
          onInput={(e) => this.handleInputEvent(e)}
          value={this.value}
        />
      );
    } else if (this.type === "radio") {
      return (
        <div class="input-label__container">
          <input
            id={`${this.name}-${this.value}`}
            type={this.type}
            class={`${this.type}-input`}
            name={this.name}
            value={this.value}
            checked={this.isChecked}
            onInput={(e) => this.handleInputEvent(e)}
          />
          <label
            class={`${this.type}-label`}
            htmlFor={`${this.name}-${this.value}`}
          >
            <slot />
          </label>
        </div>
      );
    } else if (this.type === "checkbox") {
      return (
        <div class="input-label__container">
          <input
            id={`${this.name}-${this.value}`}
            type={this.type}
            class={`${this.type}-input`}
            name={this.name}
            value={this.value}
            checked={this.isChecked}
            onInput={(e) => this.handleInputEvent(e)}
          />
          <label
            class={`${this.type}-label`}
            htmlFor={`${this.name}-${this.value}`}
          >
            <slot />
          </label>
        </div>
      );
    }
  }
}
