.spinner {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  padding-right: 0.1em;
}

.spinner div {
  display: block;
  position: absolute;
  width: 14px;
  height: 14px;
  margin: 2px;
  border: 2px solid #fff;
  border-radius: 50%;
  animation: spinner-animation 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #fff transparent transparent transparent;
}

.spinner div:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner div:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner div:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes spinner-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Theme */
.spinner--light div {
  border: 2px solid #fff;
  border-color: #fff transparent transparent transparent;
}

.spinner--blue div {
  border: 2px solid var(--color__indigo--200);
  border-color: var(--color__indigo--200) transparent transparent transparent;
}

.spinner--dark div {
  border: 2px solid rgba(0, 0, 0, 0.6);
  border-color: rgba(0, 0, 0, 0.6) transparent transparent transparent;
}
