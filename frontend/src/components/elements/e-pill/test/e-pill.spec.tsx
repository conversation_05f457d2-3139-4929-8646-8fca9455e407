import { newSpecPage } from '@stencil/core/testing';
import { EPill } from '../e-pill';

describe('e-pill', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [EPill],
      html: `<e-pill></e-pill>`,
    });
    expect(page.root).toEqualHtml(`
      <e-pill>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </e-pill>
    `);
  });
});
