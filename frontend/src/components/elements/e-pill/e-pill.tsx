import { Component, Prop, h } from "@stencil/core";

@Component({
  tag: "e-pill",
  styleUrl: "e-pill.css",
  shadow: true,
})
export class EPill {
  @Prop() color: string;

  private classList: string = "";

  componentWillLoad() {
    this.generateClassList();
  }

  generateClassList() {
    this.classList = `pill pill--${this.color}`;
  }

  render() {
    return (
      <div class={this.classList}>
        <slot></slot>
      </div>
    );
  }
}
