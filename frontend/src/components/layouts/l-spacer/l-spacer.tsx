import { Component, Prop, Host, h } from '@stencil/core';
import { LooseObjectInterface } from '../../../global/script/interfaces';

@Component({
  tag: 'l-spacer',
  styleUrl: 'l-spacer.css',
  shadow: true,
})
export class LSpacer {
  @Prop() value: number = 1;
  @Prop() variant: string = 'vertical';

  private classMap: LooseObjectInterface = {};

  componentWillLoad() {
    if (this.variant === 'vertical') {
      this.classMap.marginTop = `${this.value}em`;
      this.classMap.marginBottom = `${this.value}em`;
    } else if (this.variant === 'horizontal') {
      this.classMap.marginLeft = `${this.value}em`;
      this.classMap.marginRight = `${this.value}em`;
    }
  }

  render() {
    return <Host style={this.classMap}></Host>;
  }
}
