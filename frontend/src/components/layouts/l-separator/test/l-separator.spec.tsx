import { newSpecPage } from '@stencil/core/testing';
import { LSeparator } from '../l-separator';

describe('l-separator', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [LSeparator],
      html: `<l-separator></l-separator>`,
    });
    expect(page.root).toEqualHtml(`
      <l-separator>
        <mock:shadow-root>
          <slot></slot>
        </mock:shadow-root>
      </l-separator>
    `);
  });
});
